<?php
/**
 * 更新管理员权限系统
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始更新管理员权限系统...\n";

// 引入必要的文件
require_once 'web/ks1cck2/_lp.php';
require_once 'web/ks1cck2/education/config/database.php';

echo "数据库连接成功\n";

// 为用户角色表添加管理员相关字段
echo "\n为用户角色表添加管理员字段...\n";

$admin_fields = [
    "ADD COLUMN `admin_type` enum('none','full','part') NOT NULL DEFAULT 'none' COMMENT '管理员类型：none-无权限，full-专职管理员，part-兼任管理员' AFTER `role_id`",
    "ADD COLUMN `is_protected` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否受保护：1-受保护，0-不受保护' AFTER `admin_type`",
    "ADD COLUMN `created_by_admin_id` int(11) DEFAULT NULL COMMENT '设置管理员权限的管理员ID' AFTER `is_protected`",
    "ADD INDEX `idx_admin_type` (`admin_type`)",
    "ADD INDEX `idx_is_protected` (`is_protected`)"
];

foreach ($admin_fields as $field) {
    try {
        $sql = "ALTER TABLE " . TABLE_USER_ROLE . " " . $field;
        echo "执行SQL: " . $sql . "\n";
        $result = run_sql($sql);
        if ($result !== false) {
            echo "✓ 成功\n";
        } else {
            echo "✗ 失败\n";
        }
    } catch (Exception $e) {
        echo "✗ 错误: " . $e->getMessage() . "\n";
    }
}

// 设置默认的专职管理员
echo "\n设置默认专职管理员...\n";

try {
    // 将第一个管理员角色设置为专职管理员
    $sql = "UPDATE " . TABLE_USER_ROLE . " 
            SET admin_type = 'full', is_protected = 1 
            WHERE role_id = " . ROLE_ADMIN . " 
            ORDER BY id ASC 
            LIMIT 1";
    echo "执行SQL: " . $sql . "\n";
    $result = run_sql($sql);
    if ($result) {
        echo "✓ 默认专职管理员设置成功\n";
    } else {
        echo "✗ 默认专职管理员设置失败\n";
    }
} catch (Exception $e) {
    echo "✗ 错误: " . $e->getMessage() . "\n";
}

// 检查更新结果
echo "\n检查更新结果...\n";

try {
    $sql = "SELECT ur.*,
                   COALESCE(t.username, s.username) as username,
                   COALESCE(t.real_name, s.real_name) as real_name
            FROM " . TABLE_USER_ROLE . " ur
            LEFT JOIN edu_teacher t ON ur.user_id = t.user_id
            LEFT JOIN edu_student s ON ur.user_id = s.user_id
            WHERE ur.role_id = " . ROLE_ADMIN . "
            OR ur.admin_type != 'none'";
    $admins = get_data($sql);

    if ($admins) {
        echo "当前管理员列表：\n";
        foreach ($admins as $admin) {
            $type_text = '';
            switch ($admin['admin_type']) {
                case 'full': $type_text = '专职管理员'; break;
                case 'part': $type_text = '兼任管理员'; break;
                default: $type_text = '无管理员权限'; break;
            }
            $protected = $admin['is_protected'] ? '(受保护)' : '';
            $username = $admin['username'] ?: '未知用户';
            echo "  用户ID: {$admin['user_id']}, 用户名: {$username}, 角色ID: {$admin['role_id']}, 类型: {$type_text} {$protected}\n";
        }
    } else {
        echo "未找到管理员记录\n";
    }
} catch (Exception $e) {
    echo "✗ 检查错误: " . $e->getMessage() . "\n";
}

echo "\n管理员权限系统更新完成！\n";
?>
