<?php
/**
 * 初始化教培系统数据库
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始初始化教培系统数据库...\n";

// 引入必要的文件
require_once 'web/ks1cck2/_lp.php';

// 读取SQL文件
$sql_file = 'web/ks1cck2/education/sql/education_system.sql';
if (!file_exists($sql_file)) {
    die("SQL文件不存在: $sql_file\n");
}

$sql_content = file_get_contents($sql_file);
if (!$sql_content) {
    die("无法读取SQL文件\n");
}

echo "SQL文件读取成功\n";

// 分割SQL语句
$sql_statements = explode(';', $sql_content);

$success_count = 0;
$error_count = 0;

foreach ($sql_statements as $sql) {
    $sql = trim($sql);
    if (empty($sql)) {
        continue;
    }
    
    echo "执行SQL: " . substr($sql, 0, 50) . "...\n";
    
    try {
        $result = run_sql($sql);
        if ($result !== false) {
            $success_count++;
            echo "✓ 成功\n";
        } else {
            $error_count++;
            echo "✗ 失败\n";
        }
    } catch (Exception $e) {
        $error_count++;
        echo "✗ 错误: " . $e->getMessage() . "\n";
    }
}

echo "\n数据库初始化完成！\n";
echo "成功执行: $success_count 条SQL语句\n";
echo "失败: $error_count 条SQL语句\n";

if ($error_count == 0) {
    echo "🎉 数据库初始化成功！\n";
} else {
    echo "⚠️ 数据库初始化完成，但有部分错误\n";
}
?>
