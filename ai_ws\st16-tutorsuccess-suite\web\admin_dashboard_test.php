<?php
/**
 * 管理员功能测试页面
 * 创建时间：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 定义常量避免令牌检查
define('IN_LOGIN', true);

// 引入必要的文件
require_once __DIR__ . '/ks1cck2/_lp.php';
require_once __DIR__ . '/ks1cck2/education/config/database.php';

// 开始会话
session_start();

// 模拟管理员登录状态
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 6;
    $_SESSION['user_role'] = 'admin';
    $_SESSION['username'] = 'admin';
    $_SESSION['real_name'] = '系统管理员';
    $_SESSION['email'] = '<EMAIL>';
}

// 获取统计数据
function getSystemStats() {
    $stats = [];
    
    // 教师数量
    $stats['teacher_count'] = get_var("SELECT COUNT(*) FROM edu_teacher WHERE status = 1");
    
    // 学生数量
    $stats['student_count'] = get_var("SELECT COUNT(*) FROM edu_student WHERE status = 1");
    
    // 课程数量
    $stats['course_count'] = get_var("SELECT COUNT(*) FROM edu_course WHERE status = 1");
    
    // 待审批请假数量
    $stats['pending_leaves'] = get_var("SELECT COUNT(*) FROM edu_leave_request WHERE status = 'pending'");
    
    return $stats;
}

// 获取最近用户
function getRecentUsers() {
    $sql = "
        (SELECT id, username, real_name, email, 'teacher' as role, created_at 
         FROM edu_teacher 
         WHERE status = 1 
         ORDER BY created_at DESC LIMIT 3)
        UNION ALL
        (SELECT id, username, real_name, email, 'student' as role, created_at 
         FROM edu_student 
         WHERE status = 1 
         ORDER BY created_at DESC LIMIT 3)
        ORDER BY created_at DESC
        LIMIT 5
    ";
    return get_data($sql);
}

// 获取课程列表
function getCourses() {
    $sql = "
        SELECT c.*, t.real_name as teacher_name
        FROM edu_course c
        LEFT JOIN edu_teacher t ON c.teacher_id = t.id
        WHERE c.status = 1
        ORDER BY c.created_at DESC
        LIMIT 5
    ";
    return get_data($sql);
}

$stats = getSystemStats();
$recent_users = getRecentUsers();
$courses = getCourses();

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 特靠谱教培系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .user-list, .course-list {
            list-style: none;
            padding: 0;
        }
        .user-item, .course-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-info, .course-info {
            flex: 1;
        }
        .user-name, .course-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .user-details, .course-details {
            color: #666;
            font-size: 0.9em;
        }
        .role-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .role-teacher {
            background: #28a745;
            color: white;
        }
        .role-student {
            background: #007bff;
            color: white;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            opacity: 0.9;
            color: white;
            text-decoration: none;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-info {
            background: #17a2b8;
        }
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .action-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .action-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .action-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 管理员控制台</h1>
            <p>欢迎，<?php echo htmlspecialchars($_SESSION['real_name']); ?>！</p>
            <p>当前时间：<?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['teacher_count']; ?></div>
                <div class="stat-label">教师总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['student_count']; ?></div>
                <div class="stat-label">学生总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['course_count']; ?></div>
                <div class="stat-label">课程总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['pending_leaves']; ?></div>
                <div class="stat-label">待审批请假</div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="section">
            <h3>🚀 快速操作</h3>
            <div class="quick-actions">
                <div class="action-card">
                    <div class="action-icon">👥</div>
                    <h4>用户管理</h4>
                    <a href="user_management_test.php" class="btn">管理用户</a>
                </div>
                <div class="action-card">
                    <div class="action-icon">📚</div>
                    <h4>课程管理</h4>
                    <a href="course_management_test.php" class="btn">管理课程</a>
                </div>
                <div class="action-card">
                    <div class="action-icon">📊</div>
                    <h4>数据统计</h4>
                    <a href="statistics_test.php" class="btn">查看统计</a>
                </div>
                <div class="action-card">
                    <div class="action-icon">⚙️</div>
                    <h4>系统设置</h4>
                    <a href="settings_test.php" class="btn">系统设置</a>
                </div>
            </div>
        </div>

        <!-- 最近用户 -->
        <div class="section">
            <h3>👥 最近用户</h3>
            <?php if (!empty($recent_users)): ?>
                <ul class="user-list">
                    <?php foreach ($recent_users as $user): ?>
                        <li class="user-item">
                            <div class="user-info">
                                <div class="user-name"><?php echo htmlspecialchars($user['real_name']); ?></div>
                                <div class="user-details">
                                    <?php echo htmlspecialchars($user['username']); ?> | 
                                    <?php echo htmlspecialchars($user['email']); ?> | 
                                    <?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?>
                                </div>
                            </div>
                            <span class="role-badge role-<?php echo $user['role']; ?>">
                                <?php echo $user['role'] === 'teacher' ? '教师' : '学生'; ?>
                            </span>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p>暂无用户数据</p>
            <?php endif; ?>
        </div>

        <!-- 课程列表 -->
        <div class="section">
            <h3>📚 最近课程</h3>
            <?php if (!empty($courses)): ?>
                <ul class="course-list">
                    <?php foreach ($courses as $course): ?>
                        <li class="course-item">
                            <div class="course-info">
                                <div class="course-name"><?php echo htmlspecialchars($course['name']); ?></div>
                                <div class="course-details">
                                    教师：<?php echo htmlspecialchars($course['teacher_name'] ?: '未分配'); ?> | 
                                    价格：¥<?php echo number_format($course['price'], 2); ?> | 
                                    创建时间：<?php echo date('Y-m-d', strtotime($course['created_at'])); ?>
                                </div>
                            </div>
                            <div>
                                <a href="#" class="btn btn-info">查看</a>
                                <a href="#" class="btn btn-warning">编辑</a>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p>暂无课程数据</p>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="edu_login_test.php" class="btn">← 返回登录测试</a>
        </div>
    </div>
</body>
</html>
