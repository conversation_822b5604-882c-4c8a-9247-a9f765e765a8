<?php
/**
 * 创建学生用户测试脚本
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置HTTP_HOST避免警告
$_SERVER['HTTP_HOST'] = 'localhost';

echo "开始创建学生用户...\n";

// 引入必要的文件
require_once dirname(__DIR__) . '/web/ks1cck2/_lp.php';
require_once dirname(__DIR__) . '/web/ks1cck2/education/config/database.php';

echo "数据库连接成功\n";

// 学生信息
$students_data = [
    [
        'username' => 'student1',
        'email' => '<EMAIL>',
        'password' => '123456',
        'real_name' => '张小明',
        'grade' => '三年级',
        'class' => '三年级1班',
        'school' => '实验小学',
        'parent_name' => '张父',
        'parent_phone' => '13900139001',
        'address' => '北京市朝阳区',
        'emergency_contact' => '张母',
        'emergency_phone' => '13900139002'
    ],
    [
        'username' => 'student2',
        'email' => '<EMAIL>',
        'password' => '123456',
        'real_name' => '李小红',
        'grade' => '四年级',
        'class' => '四年级2班',
        'school' => '实验小学',
        'parent_name' => '李父',
        'parent_phone' => '13900139003',
        'address' => '北京市海淀区',
        'emergency_contact' => '李母',
        'emergency_phone' => '13900139004'
    ],
    [
        'username' => 'student3',
        'email' => '<EMAIL>',
        'password' => '123456',
        'real_name' => '王小强',
        'grade' => '五年级',
        'class' => '五年级1班',
        'school' => '实验小学',
        'parent_name' => '王父',
        'parent_phone' => '13900139005',
        'address' => '北京市西城区',
        'emergency_contact' => '王母',
        'emergency_phone' => '13900139006'
    ]
];

$student_table = TABLE_STUDENT;

foreach ($students_data as $index => $student_data) {
    try {
        // 检查是否已存在该邮箱的用户
        $sql = "SELECT id FROM $student_table WHERE email = ?s";
        $sql = prepare($sql, [$student_data['email']]);
        $existing_user = get_var($sql);
        
        if ($existing_user) {
            echo "学生已存在：{$student_data['real_name']} (ID: $existing_user)\n";
            continue;
        }
        
        // 生成学生编号
        $student_number = 'S' . date('Ymd') . sprintf('%03d', $index + 1);
        
        // 创建学生记录
        $password_hash = md5(md5($student_data['password']) . "chatgpt@2023");
        
        $sql = "INSERT INTO $student_table 
                (user_id, student_number, username, email, password, real_name, 
                 grade, class, school, parent_name, parent_phone, 
                 address, emergency_contact, emergency_phone, status, created_at) 
                VALUES (0, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, 1, NOW())";
        
        $sql = prepare($sql, [
            $student_number,
            $student_data['username'],
            $student_data['email'],
            $password_hash,
            $student_data['real_name'],
            $student_data['grade'],
            $student_data['class'],
            $student_data['school'],
            $student_data['parent_name'],
            $student_data['parent_phone'],
            $student_data['address'],
            $student_data['emergency_contact'],
            $student_data['emergency_phone']
        ]);
        
        $result = run_sql($sql);
        if ($result) {
            $student_id = last_id();
            echo "创建学生记录成功：{$student_data['real_name']} (ID: $student_id)\n";
            
            // 更新user_id字段
            $sql = "UPDATE $student_table SET user_id = ?i WHERE id = ?i";
            $sql = prepare($sql, [$student_id, $student_id]);
            run_sql($sql);
            echo "更新user_id成功\n";
        } else {
            throw new Exception("创建学生记录失败：{$student_data['real_name']}");
        }
        
    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
        continue;
    }
}

// 显示最终结果
echo "\n学生用户创建完成！\n";

$sql = "SELECT id, username, email, real_name, grade, class, status FROM $student_table ORDER BY id";
$students = get_data($sql);

if ($students) {
    echo "\n当前学生列表：\n";
    echo "ID\t用户名\t\t邮箱\t\t\t真实姓名\t年级\t\t班级\t\t状态\n";
    echo "--------------------------------------------------------------------\n";
    foreach ($students as $student) {
        $status = $student['status'] ? '正常' : '禁用';
        echo "{$student['id']}\t{$student['username']}\t\t{$student['email']}\t{$student['real_name']}\t{$student['grade']}\t\t{$student['class']}\t{$status}\n";
    }
    
    echo "\n测试登录信息：\n";
    echo "所有学生密码都是：123456\n";
    echo "邮箱：<EMAIL>, <EMAIL>, <EMAIL>\n";
}

echo "\n脚本执行完成！\n";
?>
