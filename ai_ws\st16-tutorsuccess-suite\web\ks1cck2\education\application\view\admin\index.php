<?php
/**
 * 管理员主页视图
 * 修订日期：2025-08-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

$page_title = '管理员控制台 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .dashboard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .quick-action {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s;
            display: block;
            margin-bottom: 15px;
        }
        .quick-action:hover {
            background: #e9ecef;
            text-decoration: none;
            color: #495057;
            transform: translateY(-2px);
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .welcome-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            color: white;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fa fa-user"></i> 
                    <?php echo isset($current_user['username']) ? $current_user['username'] : '管理员'; ?>
                </span>
                <a class="nav-link" href="../../../logout.php">
                    <i class="fa fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
            <h2><i class="fa fa-tachometer-alt"></i> 管理员控制台</h2>
            <p>欢迎回来！今天是 <?php echo date('Y年m月d日 H:i'); ?></p>
        </div>

        <!-- 统计数据 -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo isset($stats['total_users']) ? $stats['total_users'] : '156'; ?></div>
                    <div class="stat-label">总用户数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo isset($stats['total_courses']) ? $stats['total_courses'] : '24'; ?></div>
                    <div class="stat-label">总课程数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo isset($stats['active_courses']) ? $stats['active_courses'] : '18'; ?></div>
                    <div class="stat-label">活跃课程</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo isset($stats['pending_leaves']) ? $stats['pending_leaves'] : '5'; ?></div>
                    <div class="stat-label">待审批请假</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 快速操作 -->
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h5><i class="fa fa-bolt"></i> 快速操作</h5>
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <a href="?path=admin/users" class="quick-action">
                                <i class="fa fa-users fa-2x mb-2"></i><br>
                                用户管理
                            </a>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <a href="?path=admin/courses" class="quick-action">
                                <i class="fa fa-book fa-2x mb-2"></i><br>
                                课程管理
                            </a>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <a href="?path=admin/permissions" class="quick-action">
                                <i class="fa fa-shield-alt fa-2x mb-2"></i><br>
                                权限管理
                            </a>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <a href="?path=admin/statistics" class="quick-action">
                                <i class="fa fa-chart-bar fa-2x mb-2"></i><br>
                                数据统计
                            </a>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <a href="?path=admin/settings" class="quick-action">
                                <i class="fa fa-cog fa-2x mb-2"></i><br>
                                系统设置
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="col-md-6">
                <div class="dashboard-card">
                    <h5><i class="fa fa-clock"></i> 最近活动</h5>
                    <div class="list-group list-group-flush">
                        <?php if (isset($recent_activities) && !empty($recent_activities)): ?>
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                        <small><?php echo $activity['time']; ?></small>
                                    </div>
                                    <p class="mb-1"><?php echo htmlspecialchars($activity['description']); ?></p>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="list-group-item">
                                <p class="mb-1">新用户注册：张三</p>
                                <small>2小时前</small>
                            </div>
                            <div class="list-group-item">
                                <p class="mb-1">新课程创建：高中数学</p>
                                <small>4小时前</small>
                            </div>
                            <div class="list-group-item">
                                <p class="mb-1">请假申请审批：李四</p>
                                <small>6小时前</small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-card">
                    <h5><i class="fa fa-info-circle"></i> 系统信息</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>系统版本：</strong> 1.0.0
                        </div>
                        <div class="col-md-3">
                            <strong>PHP版本：</strong> <?php echo PHP_VERSION; ?>
                        </div>
                        <div class="col-md-3">
                            <strong>服务器时间：</strong> <?php echo date('Y-m-d H:i:s'); ?>
                        </div>
                        <div class="col-md-3">
                            <strong>运行模式：</strong> 
                            <?php echo isset($_SESSION['dev_mode']) && $_SESSION['dev_mode'] ? '开发模式' : '生产模式'; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // 添加一些交互效果
            $('.quick-action').hover(
                function() {
                    $(this).find('i').addClass('fa-bounce');
                },
                function() {
                    $(this).find('i').removeClass('fa-bounce');
                }
            );
        });
    </script>
</body>
</html>
