<?php
/**
 * 管理员控制器
 * 修订日期：2025-01-22
 */

namespace app\controller;

use app\model\Teacher as TeacherModel;
use app\model\Student as StudentModel;
use app\model\Course as CourseModel;

class Admin
{
    /**
     * 管理员主页
     */
    public function index()
    {
        // 检查权限
        $this->checkAdminAuth();
        
        $current_user = get_current_login_user();
        
        // 获取统计数据
        $stats = $this->getSystemStats();
        
        // 获取最近活动
        $recent_activities = $this->getRecentActivities();

        // 渲染视图
        include APP_PATH . 'view/admin/index.php';
    }

    /**
     * 用户管理
     */
    public function users()
    {
        // 检查权限
        $this->checkAdminAuth();

        // 获取用户列表
        $teachers = $this->getTeachers();
        $students = $this->getStudents();

        // 合并用户列表
        $users = array_merge($teachers, $students);

        // 按创建时间排序
        usort($users, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        // 渲染视图
        include '../application/view/admin/users.php';
    }

    /**
     * 课程管理
     */
    public function courses()
    {
        // 检查权限
        $this->checkAdminAuth();
        
        // 获取课程列表
        $courses = $this->getCourses();
        
        // 渲染视图
        include APP_PATH . 'view/admin/courses.php';
    }

    /**
     * 数据统计
     */
    public function statistics()
    {
        // 检查权限
        $this->checkAdminAuth();

        // 获取统计数据
        $stats = $this->getDetailedStats();

        // 渲染视图
        include APP_PATH . 'view/admin/statistics.php';
    }

    /**
     * 系统设置
     */
    public function settings()
    {
        // 检查权限
        $this->checkAdminAuth();

        // 获取系统设置
        $settings = $this->getSystemSettings();

        // 渲染视图
        include APP_PATH . 'view/admin/settings.php';
    }

    /**
     * 检查管理员权限
     */
    private function checkAdminAuth()
    {
        if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
            header('Location: ../login.php');
            exit;
        }

        // 开发模式下，从session中直接获取角色信息
        $is_admin = false;
        if (isset($_SESSION['dev_mode']) && $_SESSION['dev_mode'] && isset($_SESSION['test_role'])) {
            // 开发模式：检查测试角色
            $is_admin = ($_SESSION['test_role'] === 'admin');
        } else {
            // 正常模式：从数据库查询角色
            $current_user = get_current_login_user();
            $user_id = $current_user['id'];
            $user_roles = get_user_roles($user_id);
            $is_admin = in_array(ROLE_ADMIN, $user_roles);
        }

        if (!$is_admin) {
            echo 'Access denied';
            exit;
        }
    }

    /**
     * 获取系统统计数据
     */
    private function getSystemStats()
    {
        try {
            // 获取教师总数
            $teacher_table = TABLE_TEACHER;
            $sql = "SELECT COUNT(*) as total_teachers FROM $teacher_table WHERE status = ?i";
            $sql = prepare($sql, [STATUS_ACTIVE]);
            $total_teachers = get_var($sql) ?: 0;

            // 获取学生总数
            $student_table = TABLE_STUDENT;
            $sql = "SELECT COUNT(*) as total_students FROM $student_table WHERE status = ?i";
            $sql = prepare($sql, [STATUS_ACTIVE]);
            $total_students = get_var($sql) ?: 0;

            // 获取总课程数
            $course_table = TABLE_COURSE;
            $sql = "SELECT COUNT(*) as total_courses FROM $course_table";
            $total_courses = get_var($sql) ?: 0;

            // 获取活跃课程数（状态为1的课程）
            $sql = "SELECT COUNT(*) as active_classes FROM $course_table WHERE status = ?i";
            $sql = prepare($sql, [STATUS_ACTIVE]);
            $active_classes = get_var($sql) ?: 0;

            return [
                'total_teachers' => $total_teachers,
                'total_students' => $total_students,
                'total_courses' => $total_courses,
                'active_classes' => $active_classes
            ];
        } catch (Exception $e) {
            error_log("获取系统统计数据失败: " . $e->getMessage());
            // 返回默认值
            return [
                'total_teachers' => 0,
                'total_students' => 0,
                'total_courses' => 0,
                'active_classes' => 0
            ];
        }
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities()
    {
        try {
            $activities = [];

            // 获取最近的课程创建活动
            $course_table = TABLE_COURSE;
            $sql = "SELECT name, created_at FROM $course_table ORDER BY created_at DESC LIMIT 2";
            $recent_courses = get_data($sql);

            if ($recent_courses) {
                foreach ($recent_courses as $course) {
                    $activities[] = [
                        'title' => '课程创建',
                        'time' => date('Y-m-d H:i', strtotime($course['created_at'])),
                        'type' => 'course_create',
                        'description' => '新课程创建：' . $course['name']
                    ];
                }
            }

            // 获取最近的请假申请活动
            $leave_table = TABLE_LEAVE_REQUEST;
            $student_table = TABLE_STUDENT;

            $sql = "SELECT
                        lr.status,
                        lr.created_at,
                        lr.updated_at,
                        s.id as student_id
                    FROM $leave_table lr
                    LEFT JOIN $student_table s ON lr.student_id = s.id
                    ORDER BY lr.updated_at DESC
                    LIMIT 2";

            $recent_leaves = get_data($sql);

            if ($recent_leaves) {
                foreach ($recent_leaves as $leave) {
                    $status_text = '';
                    $activity_type = '';

                    switch ($leave['status']) {
                        case LEAVE_STATUS_PENDING:
                            $status_text = '提交了请假申请';
                            $activity_type = 'leave_request';
                            break;
                        case LEAVE_STATUS_APPROVED:
                            $status_text = '的请假申请已批准';
                            $activity_type = 'leave_approve';
                            break;
                        case LEAVE_STATUS_REJECTED:
                            $status_text = '的请假申请已拒绝';
                            $activity_type = 'leave_reject';
                            break;
                    }

                    $activities[] = [
                        'title' => '请假处理',
                        'time' => date('Y-m-d H:i', strtotime($leave['updated_at'])),
                        'type' => $activity_type,
                        'description' => '学生ID:' . ($leave['student_id'] ?: '未知') . $status_text
                    ];
                }
            }

            // 按时间排序并限制数量
            usort($activities, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });

            return array_slice($activities, 0, 3);

        } catch (Exception $e) {
            error_log("获取最近活动失败: " . $e->getMessage());
            // 返回默认活动
            return [
                [
                    'title' => '系统启动',
                    'time' => date('Y-m-d H:i'),
                    'type' => 'system',
                    'description' => '教培系统正常运行中'
                ]
            ];
        }
    }

    /**
     * 获取教师列表
     */
    private function getTeachers()
    {
        try {
            $teacher_table = TABLE_TEACHER;
            $user_role_table = TABLE_USER_ROLE;

            $sql = "SELECT
                        t.user_id as id,
                        t.username,
                        t.email,
                        t.real_name,
                        t.subject,
                        t.title,
                        t.status,
                        t.created_at,
                        ur.admin_type,
                        ur.is_protected
                    FROM $teacher_table t
                    LEFT JOIN $user_role_table ur ON t.user_id = ur.user_id
                    WHERE t.status = ?i
                    ORDER BY t.created_at DESC";

            $sql = prepare($sql, [STATUS_ACTIVE]);
            $teachers_data = get_data($sql);

            $teachers = [];
            if ($teachers_data) {
                foreach ($teachers_data as $teacher) {
                    $teachers[] = [
                        'id' => $teacher['id'],
                        'username' => $teacher['username'],
                        'email' => $teacher['email'],
                        'real_name' => $teacher['real_name'],
                        'role' => 'teacher',
                        'role_name' => '教师',
                        'status' => $teacher['status'] == STATUS_ACTIVE ? 'active' : 'inactive',
                        'created_at' => date('Y-m-d H:i', strtotime($teacher['created_at'])),
                        'admin_type' => $teacher['admin_type'] ?: 'none',
                        'is_protected' => $teacher['is_protected'] ?: 0
                    ];
                }
            }

            return $teachers;

        } catch (Exception $e) {
            error_log("获取教师列表失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取学生列表
     */
    private function getStudents()
    {
        try {
            $student_table = TABLE_STUDENT;
            $user_role_table = TABLE_USER_ROLE;

            $sql = "SELECT
                        s.user_id as id,
                        s.username,
                        s.email,
                        s.real_name,
                        s.grade,
                        s.class_name,
                        s.parent_name,
                        s.parent_phone,
                        s.status,
                        s.created_at,
                        ur.admin_type,
                        ur.is_protected
                    FROM $student_table s
                    LEFT JOIN $user_role_table ur ON s.user_id = ur.user_id
                    WHERE s.status = ?i
                    ORDER BY s.created_at DESC";

            $sql = prepare($sql, [STATUS_ACTIVE]);
            $students_data = get_data($sql);

            $students = [];
            if ($students_data) {
                foreach ($students_data as $student) {
                    $students[] = [
                        'id' => $student['id'],
                        'username' => $student['username'],
                        'email' => $student['email'],
                        'real_name' => $student['real_name'],
                        'role' => 'student',
                        'role_name' => '学生',
                        'status' => $student['status'] == STATUS_ACTIVE ? 'active' : 'inactive',
                        'created_at' => date('Y-m-d H:i', strtotime($student['created_at'])),
                        'admin_type' => $student['admin_type'] ?: 'none',
                        'is_protected' => $student['is_protected'] ?: 0
                    ];
                }
            }

            return $students;

        } catch (Exception $e) {
            error_log("获取学生列表失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取课程列表
     */
    private function getCourses()
    {
        try {
            $course_table = TABLE_COURSE;
            $teacher_table = TABLE_TEACHER;
            $student_course_table = TABLE_STUDENT_COURSE;

            // 查询课程信息，包含教师信息和学生数量
            $sql = "SELECT
                        c.id,
                        c.name,
                        c.description,
                        c.subject,
                        c.duration_minutes,
                        c.status,
                        t.teacher_number,
                        t.subject as teacher_subject,
                        COUNT(DISTINCT sc.student_id) as students
                    FROM $course_table c
                    LEFT JOIN $teacher_table t ON c.teacher_id = t.id
                    LEFT JOIN $student_course_table sc ON c.id = sc.course_id
                    GROUP BY c.id, c.name, c.description, c.subject, c.duration_minutes, c.status, t.teacher_number, t.subject
                    ORDER BY c.created_at DESC";

            $courses_data = get_data($sql);

            if (!$courses_data) {
                return [];
            }

            $courses = [];
            foreach ($courses_data as $course) {
                // 使用教师编号作为教师名称
                $teacher_name = '未分配';
                if ($course['teacher_number']) {
                    $teacher_name = $course['teacher_number'] . '(' . $course['teacher_subject'] . '老师)';
                }

                $courses[] = [
                    'id' => $course['id'],
                    'name' => $course['name'],
                    'description' => $course['description'] ?: '暂无描述',
                    'teacher' => $teacher_name,
                    'students' => (int)$course['students'],
                    'hours' => round($course['duration_minutes'] / 60, 1), // 转换为小时
                    'status' => $course['status'] == STATUS_ACTIVE ? 'active' : 'suspended'
                ];
            }

            return $courses;
        } catch (Exception $e) {
            error_log("获取课程列表失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取详细统计数据
     */
    private function getDetailedStats()
    {
        try {
            // 用户统计
            $user_role_table = TABLE_USER_ROLE;
            $teacher_table = TABLE_TEACHER;
            $student_table = TABLE_STUDENT;

            // 总用户数
            $sql = "SELECT COUNT(DISTINCT user_id) as total_users FROM $user_role_table";
            $total_users = get_var($sql) ?: 0;

            // 活跃教师数
            $sql = "SELECT COUNT(*) as active_teachers FROM $teacher_table WHERE status = ?i";
            $sql = prepare($sql, [STATUS_ACTIVE]);
            $active_teachers = get_var($sql) ?: 0;

            // 活跃学生数
            $sql = "SELECT COUNT(*) as active_students FROM $student_table WHERE status = ?i";
            $sql = prepare($sql, [STATUS_ACTIVE]);
            $active_students = get_var($sql) ?: 0;

            $active_users = $active_teachers + $active_students;
            $inactive_users = $total_users - $active_users;

            // 本月新增用户（假设按创建时间计算）
            $sql = "SELECT COUNT(DISTINCT user_id) as new_users
                    FROM $user_role_table
                    WHERE DATE_FORMAT(created_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')";
            $new_users_this_month = get_var($sql) ?: 0;

            // 课程统计
            $course_table = TABLE_COURSE;

            $sql = "SELECT COUNT(*) as total_courses FROM $course_table";
            $total_courses = get_var($sql) ?: 0;

            $sql = "SELECT COUNT(*) as active_courses FROM $course_table WHERE status = ?i";
            $sql = prepare($sql, [STATUS_ACTIVE]);
            $active_courses = get_var($sql) ?: 0;

            // 已完成课程（结课日期已过的课程）
            $sql = "SELECT COUNT(*) as completed_courses FROM $course_table WHERE end_date < CURDATE()";
            $completed_courses = get_var($sql) ?: 0;

            $suspended_courses = $total_courses - $active_courses;

            // 活动统计
            $schedule_table = TABLE_COURSE_SCHEDULE;
            $leave_table = TABLE_LEAVE_REQUEST;
            $attendance_table = TABLE_ATTENDANCE;

            // 总课时数
            $sql = "SELECT COUNT(*) as total_lessons FROM $schedule_table";
            $total_lessons = get_var($sql) ?: 0;

            // 已完成课时（已过失效日期的课程安排）
            $sql = "SELECT COUNT(*) as completed_lessons
                    FROM $schedule_table
                    WHERE expire_date < CURDATE()";
            $completed_lessons = get_var($sql) ?: 0;

            // 待审批请假
            $sql = "SELECT COUNT(*) as pending_leaves FROM $leave_table WHERE status = ?i";
            $sql = prepare($sql, [LEAVE_STATUS_PENDING]);
            $pending_leaves = get_var($sql) ?: 0;

            // 已批准请假
            $sql = "SELECT COUNT(*) as approved_leaves FROM $leave_table WHERE status = ?i";
            $sql = prepare($sql, [LEAVE_STATUS_APPROVED]);
            $approved_leaves = get_var($sql) ?: 0;

            return [
                'user_stats' => [
                    'total_users' => $total_users,
                    'new_users_this_month' => $new_users_this_month,
                    'active_users' => $active_users,
                    'inactive_users' => max(0, $inactive_users)
                ],
                'course_stats' => [
                    'total_courses' => $total_courses,
                    'active_courses' => $active_courses,
                    'completed_courses' => $completed_courses,
                    'suspended_courses' => max(0, $suspended_courses)
                ],
                'financial_stats' => [
                    'total_revenue' => 0, // 暂时设为0，需要财务表
                    'this_month_revenue' => 0,
                    'pending_payments' => 0,
                    'refunds' => 0
                ],
                'activity_stats' => [
                    'total_lessons' => $total_lessons,
                    'completed_lessons' => $completed_lessons,
                    'pending_leaves' => $pending_leaves,
                    'approved_leaves' => $approved_leaves
                ]
            ];
        } catch (Exception $e) {
            error_log("获取详细统计数据失败: " . $e->getMessage());
            // 返回默认值
            return [
                'user_stats' => [
                    'total_users' => 0,
                    'new_users_this_month' => 0,
                    'active_users' => 0,
                    'inactive_users' => 0
                ],
                'course_stats' => [
                    'total_courses' => 0,
                    'active_courses' => 0,
                    'completed_courses' => 0,
                    'suspended_courses' => 0
                ],
                'financial_stats' => [
                    'total_revenue' => 0,
                    'this_month_revenue' => 0,
                    'pending_payments' => 0,
                    'refunds' => 0
                ],
                'activity_stats' => [
                    'total_lessons' => 0,
                    'completed_lessons' => 0,
                    'pending_leaves' => 0,
                    'approved_leaves' => 0
                ]
            ];
        }
    }

    /**
     * 获取系统设置
     */
    private function getSystemSettings()
    {
        return [
            'basic_settings' => [
                'site_name' => '特靠谱教培系统',
                'site_description' => '专业的教育培训管理系统',
                'admin_email' => '<EMAIL>',
                'timezone' => 'Asia/Shanghai'
            ],
            'system_settings' => [
                'max_upload_size' => '10MB',
                'session_timeout' => 3600,
                'backup_frequency' => 'daily',
                'log_level' => 'info'
            ],
            'notification_settings' => [
                'email_notifications' => true,
                'sms_notifications' => false,
                'push_notifications' => true,
                'notification_frequency' => 'immediate'
            ]
        ];
    }

    /**
     * 添加用户
     */
    public function addUser()
    {
        // 检查权限
        $this->checkAdminAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 设置JSON响应头
            header('Content-Type: application/json');

            try {
                $username = $_POST['username'] ?? '';
                $email = $_POST['email'] ?? '';
                $password = $_POST['password'] ?? '';
                $role = $_POST['role'] ?? '';
                $real_name = $_POST['real_name'] ?? '';

                // 验证必填字段
                if (empty($username) || empty($email) || empty($password) || empty($role)) {
                    throw new Exception('请填写所有必填字段');
                }

                // 验证角色
                if (!in_array($role, ['teacher', 'student'])) {
                    throw new Exception('无效的用户角色');
                }

                // 检查用户名是否已存在（在教师表和学生表中检查）
                $teacher_table = TABLE_TEACHER;
                $student_table = TABLE_STUDENT;

                // 检查教师表中是否有相同的用户名
                $sql = "SELECT id FROM $teacher_table WHERE username = ?s";
                $sql = prepare($sql, [$username]);
                $existing_teacher = get_var($sql);

                // 检查学生表中是否有相同的用户名
                $sql = "SELECT id FROM $student_table WHERE username = ?s";
                $sql = prepare($sql, [$username]);
                $existing_student = get_var($sql);

                if ($existing_teacher || $existing_student) {
                    throw new Exception('用户名已存在');
                }

                // 生成虚拟用户ID（使用时间戳）
                $user_id = time();

                // 创建角色关联
                $role_id = ($role === 'teacher') ? ROLE_TEACHER : ROLE_STUDENT;
                $user_role_table = TABLE_USER_ROLE;
                $sql = "INSERT INTO $user_role_table (user_id, role_id, created_at, updated_at)
                        VALUES (?i, ?i, NOW(), NOW())";
                $sql = prepare($sql, [$user_id, $role_id]);
                $result = run_sql($sql);

                if (!$result) {
                    throw new Exception('创建用户角色失败');
                }

                // 如果是教师，创建教师记录
                if ($role === 'teacher') {
                    $teacher_number = 'T' . $username; // 直接使用用户名
                    $subject = $_POST['subject'] ?? '';
                    $title = $_POST['title'] ?? '';

                    $teacher_table = TABLE_TEACHER;
                    $sql = "INSERT INTO $teacher_table
                            (user_id, teacher_number, username, email, password, real_name, subject, title, status, created_at, updated_at)
                            VALUES (?i, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?i, NOW(), NOW())";
                    $sql = prepare($sql, [$user_id, $teacher_number, $username, $email, md5($password), $real_name, $subject, $title, STATUS_ACTIVE]);
                    $result = run_sql($sql);

                    if (!$result) {
                        throw new Exception('创建教师记录失败');
                    }
                }

                // 如果是学生，创建学生记录
                if ($role === 'student') {
                    $student_number = 'S' . $username; // 直接使用用户名
                    $grade = $_POST['grade'] ?? '';
                    $class_name = $_POST['class_name'] ?? '';
                    $parent_name = $_POST['parent_name'] ?? '';
                    $parent_phone = $_POST['parent_phone'] ?? '';

                    $student_table = TABLE_STUDENT;
                    $sql = "INSERT INTO $student_table
                            (user_id, student_number, username, email, password, real_name, grade, class_name, parent_name, parent_phone, status, created_at, updated_at)
                            VALUES (?i, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?i, NOW(), NOW())";
                    $sql = prepare($sql, [$user_id, $student_number, $username, $email, md5($password), $real_name, $grade, $class_name, $parent_name, $parent_phone, STATUS_ACTIVE]);
                    $result = run_sql($sql);

                    if (!$result) {
                        throw new Exception('创建学生记录失败');
                    }
                }

                echo json_encode(['status' => 'success', 'message' => '用户创建成功']);
                return;

            } catch (Exception $e) {
                echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
                return;
            }
        }

        // 显示添加用户表单
        include APP_PATH . 'view/admin/add_user.php';
    }

    /**
     * 编辑用户
     */
    public function editUser()
    {
        // 检查权限
        $this->checkAdminAuth();

        $user_id = $_GET['id'] ?? $_POST['id'] ?? 0;

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $username = $_POST['username'] ?? '';
                $email = $_POST['email'] ?? '';
                $real_name = $_POST['real_name'] ?? '';
                $role = $_POST['role'] ?? '';

                // 验证必填字段
                if (empty($username) || empty($email) || empty($role)) {
                    throw new Exception('请填写所有必填字段');
                }

                // 更新主用户记录
                $user_table = get_table_name('users');
                $sql = "UPDATE $user_table SET username = ?s, email = ?s, real_name = ?s, updated_at = NOW()
                        WHERE id = ?i";
                $sql = prepare($sql, [$username, $email, $real_name, $user_id]);
                $result = run_sql($sql);

                if (!$result) {
                    throw new Exception('更新用户信息失败');
                }

                // 更新角色特定信息
                if ($role === 'teacher') {
                    $subject = $_POST['subject'] ?? '';
                    $title = $_POST['title'] ?? '';

                    $teacher_table = TABLE_TEACHER;
                    $sql = "UPDATE $teacher_table SET subject = ?s, title = ?s, updated_at = NOW()
                            WHERE user_id = ?i";
                    $sql = prepare($sql, [$subject, $title, $user_id]);
                    run_sql($sql);
                }

                if ($role === 'student') {
                    $grade = $_POST['grade'] ?? '';
                    $class_name = $_POST['class_name'] ?? '';
                    $parent_name = $_POST['parent_name'] ?? '';
                    $parent_phone = $_POST['parent_phone'] ?? '';

                    $student_table = TABLE_STUDENT;
                    $sql = "UPDATE $student_table
                            SET grade = ?s, class_name = ?s, parent_name = ?s, parent_phone = ?s, updated_at = NOW()
                            WHERE user_id = ?i";
                    $sql = prepare($sql, [$grade, $class_name, $parent_name, $parent_phone, $user_id]);
                    run_sql($sql);
                }

                echo json_encode(['status' => 'success', 'message' => '用户信息更新成功']);
                return;

            } catch (Exception $e) {
                echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
                return;
            }
        }

        // 获取用户信息
        $user = $this->getUserById($user_id);
        if (!$user) {
            echo '用户不存在';
            return;
        }

        // 显示编辑用户表单
        include APP_PATH . 'view/admin/edit_user.php';
    }

    /**
     * 删除用户
     */
    public function deleteUser()
    {
        // 检查权限
        $this->checkAdminAuth();

        $user_id = $_POST['id'] ?? 0;

        try {
            if (!$user_id) {
                throw new Exception('用户ID不能为空');
            }

            // 检查用户是否存在
            $user_table = get_table_name('users');
            $sql = "SELECT id FROM $user_table WHERE id = ?i";
            $sql = prepare($sql, [$user_id]);
            $user = get_var($sql);

            if (!$user) {
                throw new Exception('用户不存在');
            }

            // 检查是否有关联数据
            $course_table = TABLE_COURSE;
            $sql = "SELECT COUNT(*) FROM $course_table c
                    LEFT JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                    WHERE t.user_id = ?i";
            $sql = prepare($sql, [$user_id]);
            $course_count = get_var($sql);

            if ($course_count > 0) {
                throw new Exception('该用户有关联的课程，无法删除');
            }

            // 删除角色关联
            $user_role_table = TABLE_USER_ROLE;
            $sql = "DELETE FROM $user_role_table WHERE user_id = ?i";
            $sql = prepare($sql, [$user_id]);
            run_sql($sql);

            // 删除教师或学生记录
            $teacher_table = TABLE_TEACHER;
            $sql = "DELETE FROM $teacher_table WHERE user_id = ?i";
            $sql = prepare($sql, [$user_id]);
            run_sql($sql);

            $student_table = TABLE_STUDENT;
            $sql = "DELETE FROM $student_table WHERE user_id = ?i";
            $sql = prepare($sql, [$user_id]);
            run_sql($sql);

            // 删除主用户记录
            $sql = "DELETE FROM $user_table WHERE id = ?i";
            $sql = prepare($sql, [$user_id]);
            $result = run_sql($sql);

            if (!$result) {
                throw new Exception('删除用户失败');
            }

            echo json_encode(['status' => 'success', 'message' => '用户删除成功']);

        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    /**
     * 根据ID获取用户信息
     */
    private function getUserById($user_id)
    {
        try {
            $user_role_table = TABLE_USER_ROLE;
            $teacher_table = TABLE_TEACHER;
            $student_table = TABLE_STUDENT;

            $sql = "SELECT
                        ur.user_id as id,
                        ur.role_id,
                        ur.admin_type,
                        ur.is_protected,
                        COALESCE(t.username, s.username) as username,
                        COALESCE(t.email, s.email) as email,
                        COALESCE(t.real_name, s.real_name) as real_name,
                        t.teacher_number,
                        t.subject as teacher_subject,
                        t.title as teacher_title,
                        s.student_number,
                        s.grade,
                        s.class_name,
                        s.parent_name,
                        s.parent_phone
                    FROM $user_role_table ur
                    LEFT JOIN $teacher_table t ON ur.user_id = t.user_id
                    LEFT JOIN $student_table s ON ur.user_id = s.user_id
                    WHERE ur.user_id = ?i";

            $sql = prepare($sql, [$user_id]);
            $user = get_line($sql);

            if ($user) {
                // 确定用户角色
                if ($user['role_id'] == ROLE_TEACHER) {
                    $user['role'] = 'teacher';
                } elseif ($user['role_id'] == ROLE_STUDENT) {
                    $user['role'] = 'student';
                } else {
                    $user['role'] = 'admin';
                }
            }

            return $user;

        } catch (Exception $e) {
            error_log("获取用户信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 权限管理页面
     */
    public function permissions()
    {
        // 检查权限
        $this->checkAdminAuth();

        // 获取当前用户信息（开发模式下使用模拟数据）
        if (isset($_SESSION['dev_mode']) && $_SESSION['dev_mode']) {
            $current_user = ['id' => 1, 'username' => '测试管理员'];
            $current_admin = ['admin_type' => ADMIN_TYPE_FULL, 'is_protected' => 1];
        } else {
            $current_user = get_current_login_user();
            $current_admin = $this->getCurrentAdminInfo($current_user['id']);
        }

        // 获取所有教师列表（可以设置为管理员的用户）
        $teachers = $this->getTeachersForPermission();

        // 渲染视图
        include APP_PATH . 'view/admin/permissions.php';
    }

    /**
     * 设置管理员权限
     */
    public function setAdminPermission()
    {
        // 检查权限
        $this->checkAdminAuth();

        // 设置JSON响应头
        header('Content-Type: application/json');

        try {
            $user_id = $_POST['user_id'] ?? 0;
            $admin_type = $_POST['admin_type'] ?? '';
            $is_protected = isset($_POST['is_protected']) ? 1 : 0;

            // 验证参数
            if (!$user_id || !in_array($admin_type, [ADMIN_TYPE_NONE, ADMIN_TYPE_PART])) {
                throw new Exception('参数错误');
            }

            // 获取当前管理员信息
            $current_user = get_current_login_user();
            $current_admin = $this->getCurrentAdminInfo($current_user['id']);

            // 检查操作权限
            if (!$this->canManagePermission($current_admin, $user_id, $admin_type)) {
                throw new Exception('权限不足');
            }

            // 更新用户权限
            $user_role_table = TABLE_USER_ROLE;
            $sql = "UPDATE $user_role_table
                    SET admin_type = ?s, is_protected = ?i, created_by_admin_id = ?i, updated_at = NOW()
                    WHERE user_id = ?i";
            $sql = prepare($sql, [$admin_type, $is_protected, $current_user['id'], $user_id]);
            $result = run_sql($sql);

            if (!$result) {
                throw new Exception('权限设置失败');
            }

            echo json_encode(['status' => 'success', 'message' => '权限设置成功']);

        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    /**
     * 获取当前管理员信息
     */
    private function getCurrentAdminInfo($user_id)
    {
        try {
            $user_role_table = TABLE_USER_ROLE;
            $sql = "SELECT * FROM $user_role_table WHERE user_id = ?i";
            $sql = prepare($sql, [$user_id]);
            return get_line($sql);
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 获取可以设置权限的教师列表
     */
    private function getTeachersForPermission()
    {
        try {
            $teacher_table = TABLE_TEACHER;
            $user_role_table = TABLE_USER_ROLE;

            $sql = "SELECT
                        t.user_id,
                        t.username,
                        t.real_name,
                        t.subject,
                        t.title,
                        ur.admin_type,
                        ur.is_protected,
                        ur.created_by_admin_id
                    FROM $teacher_table t
                    LEFT JOIN $user_role_table ur ON t.user_id = ur.user_id
                    WHERE t.status = ?i
                    ORDER BY ur.admin_type DESC, t.username ASC";

            $sql = prepare($sql, [STATUS_ACTIVE]);
            return get_data($sql) ?: [];

        } catch (Exception $e) {
            error_log("获取教师权限列表失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 检查是否可以管理权限
     */
    private function canManagePermission($current_admin, $target_user_id, $new_admin_type)
    {
        // 专职管理员可以管理所有权限
        if ($current_admin['admin_type'] === ADMIN_TYPE_FULL) {
            return true;
        }

        // 兼任管理员只能设置其他教师为兼任管理员，不能取消权限或设置保护状态
        if ($current_admin['admin_type'] === ADMIN_TYPE_PART) {
            // 获取目标用户信息
            $target_user = $this->getCurrentAdminInfo($target_user_id);

            // 不能修改受保护的用户
            if ($target_user && $target_user['is_protected']) {
                return false;
            }

            // 只能设置为兼任管理员，不能取消权限
            if ($new_admin_type !== ADMIN_TYPE_PART) {
                return false;
            }

            return true;
        }

        // 无管理员权限的用户不能管理权限
        return false;
    }
}
