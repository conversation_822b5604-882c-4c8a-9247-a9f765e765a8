<?php
/**
 * 管理员系统设置页面
 * 修订日期：2025-08-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

$page_title = '系统设置 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .setting-section {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .setting-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .setting-item:last-child {
            border-bottom: none;
        }
        .setting-label {
            font-weight: 500;
        }
        .setting-description {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="?path=admin">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fa fa-user"></i> 
                    <?php echo isset($current_user['username']) ? $current_user['username'] : '管理员'; ?>
                </span>
                <a class="nav-link" href="../../../logout.php">
                    <i class="fa fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="?path=admin" style="color: white;">管理员控制台</a></li>
                <li class="breadcrumb-item active" style="color: white;">系统设置</li>
            </ol>
        </nav>

        <!-- 系统设置内容 -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fa fa-cog"></i> 系统设置</h4>
                <button class="btn btn-primary" onclick="saveSettings()">
                    <i class="fa fa-save"></i> 保存设置
                </button>
            </div>

            <!-- 基本设置 -->
            <div class="setting-section">
                <h6><i class="fa fa-info-circle"></i> 基本设置</h6>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">网站名称</div>
                        <div class="setting-description">显示在页面标题和导航栏的网站名称</div>
                    </div>
                    <div style="width: 300px;">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($settings['basic_settings']['site_name']); ?>" id="site_name">
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">网站描述</div>
                        <div class="setting-description">网站的简短描述，用于SEO和介绍</div>
                    </div>
                    <div style="width: 300px;">
                        <textarea class="form-control" rows="2" id="site_description"><?php echo htmlspecialchars($settings['basic_settings']['site_description']); ?></textarea>
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">管理员邮箱</div>
                        <div class="setting-description">系统管理员的联系邮箱</div>
                    </div>
                    <div style="width: 300px;">
                        <input type="email" class="form-control" value="<?php echo htmlspecialchars($settings['basic_settings']['admin_email']); ?>" id="admin_email">
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">时区设置</div>
                        <div class="setting-description">系统使用的时区</div>
                    </div>
                    <div style="width: 300px;">
                        <select class="form-select" id="timezone">
                            <option value="Asia/Shanghai" <?php echo $settings['basic_settings']['timezone'] === 'Asia/Shanghai' ? 'selected' : ''; ?>>Asia/Shanghai</option>
                            <option value="Asia/Beijing" <?php echo $settings['basic_settings']['timezone'] === 'Asia/Beijing' ? 'selected' : ''; ?>>Asia/Beijing</option>
                            <option value="UTC" <?php echo $settings['basic_settings']['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div class="setting-section">
                <h6><i class="fa fa-server"></i> 系统设置</h6>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">最大上传文件大小</div>
                        <div class="setting-description">单个文件的最大上传大小限制</div>
                    </div>
                    <div style="width: 300px;">
                        <select class="form-select" id="max_upload_size">
                            <option value="5MB" <?php echo $settings['system_settings']['max_upload_size'] === '5MB' ? 'selected' : ''; ?>>5MB</option>
                            <option value="10MB" <?php echo $settings['system_settings']['max_upload_size'] === '10MB' ? 'selected' : ''; ?>>10MB</option>
                            <option value="20MB" <?php echo $settings['system_settings']['max_upload_size'] === '20MB' ? 'selected' : ''; ?>>20MB</option>
                            <option value="50MB" <?php echo $settings['system_settings']['max_upload_size'] === '50MB' ? 'selected' : ''; ?>>50MB</option>
                        </select>
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">会话超时时间</div>
                        <div class="setting-description">用户会话的超时时间（秒）</div>
                    </div>
                    <div style="width: 300px;">
                        <input type="number" class="form-control" value="<?php echo $settings['system_settings']['session_timeout']; ?>" id="session_timeout" min="300" max="86400">
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">备份频率</div>
                        <div class="setting-description">自动备份数据库的频率</div>
                    </div>
                    <div style="width: 300px;">
                        <select class="form-select" id="backup_frequency">
                            <option value="daily" <?php echo $settings['system_settings']['backup_frequency'] === 'daily' ? 'selected' : ''; ?>>每日</option>
                            <option value="weekly" <?php echo $settings['system_settings']['backup_frequency'] === 'weekly' ? 'selected' : ''; ?>>每周</option>
                            <option value="monthly" <?php echo $settings['system_settings']['backup_frequency'] === 'monthly' ? 'selected' : ''; ?>>每月</option>
                            <option value="never" <?php echo $settings['system_settings']['backup_frequency'] === 'never' ? 'selected' : ''; ?>>从不</option>
                        </select>
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">日志级别</div>
                        <div class="setting-description">系统日志记录的详细程度</div>
                    </div>
                    <div style="width: 300px;">
                        <select class="form-select" id="log_level">
                            <option value="debug" <?php echo $settings['system_settings']['log_level'] === 'debug' ? 'selected' : ''; ?>>调试</option>
                            <option value="info" <?php echo $settings['system_settings']['log_level'] === 'info' ? 'selected' : ''; ?>>信息</option>
                            <option value="warning" <?php echo $settings['system_settings']['log_level'] === 'warning' ? 'selected' : ''; ?>>警告</option>
                            <option value="error" <?php echo $settings['system_settings']['log_level'] === 'error' ? 'selected' : ''; ?>>错误</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 通知设置 -->
            <div class="setting-section">
                <h6><i class="fa fa-bell"></i> 通知设置</h6>
                
                <div class="setting-item">
                    <div>
                        <div class="setting-label">邮件通知</div>
                        <div class="setting-description">启用邮件通知功能</div>
                    </div>
                    <div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="email_notifications" <?php echo $settings['notification_settings']['email_notifications'] ? 'checked' : ''; ?>>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">短信通知</div>
                        <div class="setting-description">启用短信通知功能</div>
                    </div>
                    <div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sms_notifications" <?php echo $settings['notification_settings']['sms_notifications'] ? 'checked' : ''; ?>>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">推送通知</div>
                        <div class="setting-description">启用浏览器推送通知</div>
                    </div>
                    <div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="push_notifications" <?php echo $settings['notification_settings']['push_notifications'] ? 'checked' : ''; ?>>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div>
                        <div class="setting-label">通知频率</div>
                        <div class="setting-description">发送通知的频率</div>
                    </div>
                    <div style="width: 300px;">
                        <select class="form-select" id="notification_frequency">
                            <option value="immediate" <?php echo $settings['notification_settings']['notification_frequency'] === 'immediate' ? 'selected' : ''; ?>>立即</option>
                            <option value="hourly" <?php echo $settings['notification_settings']['notification_frequency'] === 'hourly' ? 'selected' : ''; ?>>每小时</option>
                            <option value="daily" <?php echo $settings['notification_settings']['notification_frequency'] === 'daily' ? 'selected' : ''; ?>>每日</option>
                            <option value="weekly" <?php echo $settings['notification_settings']['notification_frequency'] === 'weekly' ? 'selected' : ''; ?>>每周</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="d-flex justify-content-between">
                <div>
                    <button class="btn btn-outline-warning me-2" onclick="resetSettings()">
                        <i class="fa fa-undo"></i> 重置为默认
                    </button>
                    <button class="btn btn-outline-info" onclick="exportSettings()">
                        <i class="fa fa-download"></i> 导出设置
                    </button>
                </div>
                <div>
                    <button class="btn btn-outline-secondary me-2" onclick="cancelChanges()">
                        <i class="fa fa-times"></i> 取消
                    </button>
                    <button class="btn btn-primary" onclick="saveSettings()">
                        <i class="fa fa-save"></i> 保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function saveSettings() {
            // 收集所有设置数据
            const settings = {
                basic_settings: {
                    site_name: $('#site_name').val(),
                    site_description: $('#site_description').val(),
                    admin_email: $('#admin_email').val(),
                    timezone: $('#timezone').val()
                },
                system_settings: {
                    max_upload_size: $('#max_upload_size').val(),
                    session_timeout: $('#session_timeout').val(),
                    backup_frequency: $('#backup_frequency').val(),
                    log_level: $('#log_level').val()
                },
                notification_settings: {
                    email_notifications: $('#email_notifications').is(':checked'),
                    sms_notifications: $('#sms_notifications').is(':checked'),
                    push_notifications: $('#push_notifications').is(':checked'),
                    notification_frequency: $('#notification_frequency').val()
                }
            };
            
            // 这里应该发送AJAX请求保存设置
            alert('设置保存功能开发中...\n\n收集到的设置：\n' + JSON.stringify(settings, null, 2));
        }
        
        function resetSettings() {
            if (confirm('确定要重置所有设置为默认值吗？')) {
                alert('重置设置功能开发中...');
            }
        }
        
        function exportSettings() {
            alert('导出设置功能开发中...');
        }
        
        function cancelChanges() {
            if (confirm('确定要取消所有更改吗？')) {
                location.reload();
            }
        }
    </script>
</body>
</html>
