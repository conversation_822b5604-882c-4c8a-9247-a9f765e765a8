<?php
/**
 * 添加学生测试数据
 * 创建时间：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 引入必要的文件
require_once dirname(__DIR__) . '/web/ks1cck2/_lp.php';
require_once dirname(__DIR__) . '/web/ks1cck2/education/config/database.php';

echo "<h2>添加学生测试数据</h2>\n";
echo "<p>执行时间：" . date('Y-m-d H:i:s') . "</p>\n";

// 检查是否已有学生数据
$student_count = get_var("SELECT COUNT(*) FROM edu_student");
echo "<p>当前学生数量：$student_count</p>\n";

// 学生测试数据
$students = [
    [
        'username' => 'teststudent1',
        'email' => '<EMAIL>',
        'password' => md5(md5('123456') . "chatgpt@2023"),
        'real_name' => '张小明',
        'phone' => '13800138001',
        'parent_phone' => '13900139001',
        'grade' => '三年级',
        'class_name' => '三年级1班',
        'school' => '实验小学',
        'address' => '北京市朝阳区',
        'emergency_contact' => '张父',
        'emergency_phone' => '13900139001',
        'status' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ],
    [
        'username' => 'teststudent2',
        'email' => '<EMAIL>',
        'password' => md5(md5('123456') . "chatgpt@2023"),
        'real_name' => '李小红',
        'phone' => '13800138002',
        'parent_phone' => '13900139002',
        'grade' => '四年级',
        'class_name' => '四年级2班',
        'school' => '实验小学',
        'address' => '北京市海淀区',
        'emergency_contact' => '李母',
        'emergency_phone' => '13900139002',
        'status' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ],
    [
        'username' => 'teststudent3',
        'email' => '<EMAIL>',
        'password' => md5(md5('123456') . "chatgpt@2023"),
        'real_name' => '王小强',
        'phone' => '13800138003',
        'parent_phone' => '13900139003',
        'grade' => '五年级',
        'class_name' => '五年级1班',
        'school' => '实验小学',
        'address' => '北京市西城区',
        'emergency_contact' => '王父',
        'emergency_phone' => '13900139003',
        'status' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]
];

echo "<h3>开始添加学生数据</h3>\n";

foreach ($students as $index => $student) {
    // 检查邮箱是否已存在
    $existing = get_var("SELECT COUNT(*) FROM edu_student WHERE email = ?s", [$student['email']]);
    
    if ($existing > 0) {
        echo "<p style='color: orange;'>⚠️ 学生 {$student['real_name']} ({$student['email']}) 已存在，跳过</p>\n";
        continue;
    }
    
    // 插入学生数据
    $sql = "INSERT INTO edu_student (
        username, email, password, real_name, phone, parent_phone, 
        grade, class_name, school, address, emergency_contact, emergency_phone,
        status, created_at, updated_at
    ) VALUES (
        ?s, ?s, ?s, ?s, ?s, ?s, 
        ?s, ?s, ?s, ?s, ?s, ?s,
        ?i, ?s, ?s
    )";
    
    $params = [
        $student['username'], $student['email'], $student['password'], 
        $student['real_name'], $student['phone'], $student['parent_phone'],
        $student['grade'], $student['class_name'], $student['school'], 
        $student['address'], $student['emergency_contact'], $student['emergency_phone'],
        $student['status'], $student['created_at'], $student['updated_at']
    ];
    
    $sql = prepare($sql, $params);
    $result = run_sql($sql);
    
    if ($result) {
        echo "<p style='color: green;'>✅ 成功添加学生：{$student['real_name']} ({$student['email']})</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 添加学生失败：{$student['real_name']} ({$student['email']})</p>\n";
    }
}

// 检查最终结果
$final_count = get_var("SELECT COUNT(*) FROM edu_student");
echo "<h3>添加完成</h3>\n";
echo "<p>最终学生数量：$final_count</p>\n";

if ($final_count > 0) {
    echo "<h4>学生列表：</h4>\n";
    $students_list = get_data("SELECT id, username, email, real_name, grade, class_name, status FROM edu_student ORDER BY id");
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>真实姓名</th><th>年级</th><th>班级</th><th>状态</th></tr>\n";
    foreach ($students_list as $student) {
        $status = $student['status'] ? '正常' : '禁用';
        echo "<tr><td>{$student['id']}</td><td>{$student['username']}</td><td>{$student['email']}</td><td>{$student['real_name']}</td><td>{$student['grade']}</td><td>{$student['class_name']}</td><td>$status</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<h4>测试账号信息：</h4>\n";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<p><strong>学生测试账号（密码都是：123456）：</strong></p>\n";
    echo "<ul>\n";
    echo "<li><EMAIL> - 张小明（三年级1班）</li>\n";
    echo "<li><EMAIL> - 李小红（四年级2班）</li>\n";
    echo "<li><EMAIL> - 王小强（五年级1班）</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p>执行完成时间：" . date('Y-m-d H:i:s') . "</p>\n";
?>
