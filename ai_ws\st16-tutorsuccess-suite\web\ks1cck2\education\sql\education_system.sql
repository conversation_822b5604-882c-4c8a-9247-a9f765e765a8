-- 特靠谱教培系统数据库表结构
-- 创建时间：2025-01-22
-- 修订日期：2025-08-22

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户角色表
-- ----------------------------
DROP TABLE IF EXISTS `edu_user_role`;
CREATE TABLE `edu_user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID：1-管理员，2-教师，3-学生',
  `admin_type` enum('none','full','part') NOT NULL DEFAULT 'none' COMMENT '管理员类型：none-无权限，full-专职管理员，part-兼任管理员',
  `is_protected` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否受保护：1-受保护，0-不受保护',
  `created_by_admin_id` int(11) DEFAULT NULL COMMENT '设置管理员权限的管理员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_admin_type` (`admin_type`),
  KEY `idx_is_protected` (`is_protected`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- ----------------------------
-- 教师表
-- ----------------------------
DROP TABLE IF EXISTS `edu_teacher`;
CREATE TABLE `edu_teacher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `teacher_number` varchar(50) NOT NULL COMMENT '教师编号',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `email` varchar(200) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码（MD5加密）',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `subject` varchar(100) NOT NULL COMMENT '主教科目',
  `title` varchar(50) DEFAULT NULL COMMENT '职称',
  `qualification` varchar(200) DEFAULT NULL COMMENT '资质证书',
  `experience_years` int(11) DEFAULT 0 COMMENT '教学经验年数',
  `introduction` text COMMENT '个人简介',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-停用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  UNIQUE KEY `uk_teacher_number` (`teacher_number`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_subject` (`subject`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师信息表';

-- ----------------------------
-- 学生表
-- ----------------------------
DROP TABLE IF EXISTS `edu_student`;
CREATE TABLE `edu_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `student_number` varchar(50) NOT NULL COMMENT '学号',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `email` varchar(200) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码（MD5加密）',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `grade` varchar(50) DEFAULT NULL COMMENT '年级',
  `class_name` varchar(50) DEFAULT NULL COMMENT '班级',
  `school` varchar(200) DEFAULT NULL COMMENT '学校',
  `parent_name` varchar(100) DEFAULT NULL COMMENT '家长姓名',
  `parent_phone` varchar(20) DEFAULT NULL COMMENT '家长电话',
  `parent_wechat` varchar(100) DEFAULT NULL COMMENT '家长微信',
  `emergency_contact` varchar(100) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系电话',
  `address` varchar(500) DEFAULT NULL COMMENT '家庭地址',
  `notes` text COMMENT '备注信息',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-停用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  UNIQUE KEY `uk_student_number` (`student_number`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_grade` (`grade`),
  KEY `idx_class` (`class_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生信息表';

-- ----------------------------
-- 教室表
-- ----------------------------
DROP TABLE IF EXISTS `edu_classroom`;
CREATE TABLE `edu_classroom` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '教室名称',
  `location` varchar(200) DEFAULT NULL COMMENT '教室位置',
  `capacity` int(11) DEFAULT 0 COMMENT '容纳人数',
  `equipment` text COMMENT '设备信息',
  `notes` text COMMENT '备注信息',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-可用，0-维护中',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教室信息表';

-- ----------------------------
-- 课程表
-- ----------------------------
DROP TABLE IF EXISTS `edu_course`;
CREATE TABLE `edu_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '课程名称',
  `subject` varchar(100) NOT NULL COMMENT '科目',
  `teacher_id` int(11) NOT NULL COMMENT '授课教师ID',
  `description` text COMMENT '课程描述',
  `grade_level` varchar(50) DEFAULT NULL COMMENT '适用年级',
  `max_students` int(11) DEFAULT 30 COMMENT '最大学生数',
  `price` decimal(10,2) DEFAULT 0.00 COMMENT '课程价格',
  `duration_minutes` int(11) DEFAULT 90 COMMENT '课程时长（分钟）',
  `start_date` date DEFAULT NULL COMMENT '开课日期',
  `end_date` date DEFAULT NULL COMMENT '结课日期',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-停用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_subject` (`subject`),
  KEY `idx_status` (`status`),
  KEY `idx_start_date` (`start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程信息表';

-- ----------------------------
-- 课程安排表
-- ----------------------------
DROP TABLE IF EXISTS `edu_course_schedule`;
CREATE TABLE `edu_course_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `classroom_id` int(11) DEFAULT NULL COMMENT '教室ID',
  `day_of_week` tinyint(1) NOT NULL COMMENT '星期几：1-7（周一到周日）',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `effective_date` date DEFAULT NULL COMMENT '生效日期',
  `expire_date` date DEFAULT NULL COMMENT '失效日期',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-停用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_classroom_id` (`classroom_id`),
  KEY `idx_day_time` (`day_of_week`, `start_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程安排表';

-- ----------------------------
-- 学生课程关联表
-- ----------------------------
DROP TABLE IF EXISTS `edu_student_course`;
CREATE TABLE `edu_student_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL COMMENT '学生ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `enroll_date` date NOT NULL COMMENT '报名日期',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-退课',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_course` (`student_id`, `course_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生课程关联表';

-- ----------------------------
-- 请假申请表
-- ----------------------------
DROP TABLE IF EXISTS `edu_leave_request`;
CREATE TABLE `edu_leave_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL COMMENT '学生ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `schedule_id` int(11) DEFAULT NULL COMMENT '课程安排ID',
  `leave_type` tinyint(1) NOT NULL DEFAULT 2 COMMENT '请假类型：1-病假，2-事假，3-其他',
  `start_date` date NOT NULL COMMENT '请假开始日期',
  `end_date` date NOT NULL COMMENT '请假结束日期',
  `reason` text NOT NULL COMMENT '请假原因',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0-待审批，1-已批准，2-已拒绝',
  `approved_by` int(11) DEFAULT NULL COMMENT '审批人ID',
  `approved_at` timestamp NULL DEFAULT NULL COMMENT '审批时间',
  `comment` text COMMENT '审批意见',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_schedule_id` (`schedule_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_date` (`start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='请假申请表';

-- ----------------------------
-- 考勤记录表
-- ----------------------------
DROP TABLE IF EXISTS `edu_attendance`;
CREATE TABLE `edu_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL COMMENT '学生ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `schedule_id` int(11) DEFAULT NULL COMMENT '课程安排ID',
  `attendance_date` date NOT NULL COMMENT '考勤日期',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '考勤状态：0-缺席，1-出席，2-请假',
  `notes` text COMMENT '备注',
  `recorded_by` int(11) DEFAULT NULL COMMENT '记录人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_course_date` (`student_id`, `course_id`, `attendance_date`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_schedule_id` (`schedule_id`),
  KEY `idx_attendance_date` (`attendance_date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考勤记录表';

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 插入测试数据
-- ----------------------------

-- 插入教室数据
INSERT INTO `edu_classroom` (`name`, `location`, `capacity`, `equipment`, `status`) VALUES
('教室A101', '一楼东侧', 30, '投影仪、白板、空调、音响', 1),
('教室A102', '一楼西侧', 25, '投影仪、白板、空调', 1),
('教室A103', '一楼中间', 20, '电脑、投影仪、白板', 1),
('多媒体教室', '二楼西侧', 40, '电脑、投影仪、音响设备、智能黑板', 1),
('实验室', '二楼东侧', 15, '实验设备、投影仪、通风设备', 1);

-- 插入用户角色数据
INSERT INTO `edu_user_role` (`user_id`, `role_id`, `admin_type`, `is_protected`) VALUES
(1, 1, 'full', 1),  -- 专职管理员（受保护）
(2, 2, 'none', 0),  -- 教师
(3, 2, 'none', 0),  -- 教师
(4, 3, 'none', 0),  -- 学生
(5, 3, 'none', 0),  -- 学生
(6, 3, 'none', 0),  -- 学生
(7, 3, 'none', 0),  -- 学生
(8, 3, 'none', 0);  -- 学生

-- 插入教师数据
INSERT INTO `edu_teacher` (`user_id`, `teacher_number`, `username`, `email`, `password`, `real_name`, `subject`, `title`, `qualification`, `experience_years`, `introduction`, `status`) VALUES
(2, 'T001', 'teacher001', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', '李老师', '数学', '高级教师', '数学教师资格证、高级教师职称证书', 8, '专业从事初高中数学教学，擅长代数和几何教学，教学经验丰富。', 1),
(3, 'T002', 'teacher002', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', '王老师', '英语', '中级教师', '英语教师资格证、英语专业八级证书', 5, '英语专业毕业，口语流利，擅长英语口语和写作教学。', 1);

-- 插入课程数据
INSERT INTO `edu_course` (`name`, `subject`, `teacher_id`, `description`, `grade_level`, `max_students`, `price`, `duration_minutes`, `start_date`, `end_date`, `status`) VALUES
('高中数学基础', '数学', 1, '高中数学基础课程，包含代数、几何、概率统计等内容', '高中', 30, 2000.00, 2400, '2025-01-01', '2025-06-30', 1),
('初中英语综合', '英语', 2, '初中英语综合课程，提升听说读写能力', '初中', 25, 1500.00, 1800, '2025-02-01', '2025-07-31', 1),
('高中物理实验', '物理', 1, '高中物理课程，涵盖力学、电学、光学等内容', '高中', 20, 1800.00, 2100, '2025-01-15', '2025-05-15', 0);
