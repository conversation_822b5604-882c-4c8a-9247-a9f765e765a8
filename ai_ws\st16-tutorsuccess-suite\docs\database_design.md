# 特靠谱教培系统数据库设计文档

> 修订日期：2025-08-22

## 概述

特靠谱教培系统采用MySQL数据库，使用`edu_`作为表名前缀。系统设计遵循第三范式，确保数据的一致性和完整性。本文档详细介绍了系统中实际使用的数据表结构、关键字段说明以及重要的数据关系。

## 数据库配置

- **数据库名称**: `pubhelper4dev`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_general_ci`
- **表前缀**: `edu_`
- **引擎**: `InnoDB`

## 核心数据表

### 1. 用户角色表 (edu_user_role)

**表说明**: 用户角色关联表，管理用户的身份和权限信息

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| user_id | int(11) | NOT NULL, INDEX | 用户ID |
| role_id | int(11) | NOT NULL, INDEX | 角色ID：1-管理员，2-教师，3-学生 |
| admin_type | enum('none','full','part') | NOT NULL, DEFAULT 'none' | 管理员类型 |
| is_protected | tinyint(1) | NOT NULL, DEFAULT 0 | 是否受保护 |
| created_by_admin_id | int(11) | NULL | 设置管理员权限的管理员ID |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `uk_user_role` (user_id, role_id) - 唯一索引
- `idx_admin_type` (admin_type) - 普通索引
- `idx_is_protected` (is_protected) - 普通索引

**特殊字段说明**:
- `admin_type`: 管理员权限层级
  - `none`: 无管理员权限
  - `full`: 专职管理员（最高权限）
  - `part`: 兼任管理员（部分权限）
- `is_protected`: 保护机制，防止兼任管理员误操作

### 2. 教师表 (edu_teacher)

**表说明**: 教师信息表，存储教师的基本信息和教学相关数据

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| user_id | int(11) | NOT NULL, UNIQUE | 用户ID |
| teacher_number | varchar(50) | NOT NULL, UNIQUE | 教师编号 |
| username | varchar(100) | NOT NULL, UNIQUE | 用户名 |
| email | varchar(200) | NOT NULL, UNIQUE | 邮箱 |
| password | varchar(255) | NOT NULL | 密码（MD5加密） |
| real_name | varchar(100) | NULL | 真实姓名 |
| subject | varchar(100) | NOT NULL | 主教科目 |
| title | varchar(50) | NULL | 职称 |
| qualification | varchar(200) | NULL | 资质证书 |
| experience_years | int(11) | DEFAULT 0 | 教学经验年数 |
| introduction | text | NULL | 个人简介 |
| status | tinyint(1) | NOT NULL, DEFAULT 1 | 状态：1-正常，0-停用 |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `uk_teacher_number` (teacher_number) - 唯一索引
- `uk_username` (username) - 唯一索引
- `uk_email` (email) - 唯一索引
- `idx_subject` (subject) - 普通索引

**特殊设计**:
- 教师表包含完整的用户基本信息（username, email, password），实现独立的用户系统
- `teacher_number` 采用"T+用户名"格式，如"Ttestteacher"

### 3. 学生表 (edu_student)

**表说明**: 学生信息表，存储学生的基本信息和家长联系方式

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| user_id | int(11) | NOT NULL, UNIQUE | 用户ID |
| student_number | varchar(50) | NOT NULL, UNIQUE | 学号 |
| username | varchar(100) | NOT NULL, UNIQUE | 用户名 |
| email | varchar(200) | NOT NULL, UNIQUE | 邮箱 |
| password | varchar(255) | NOT NULL | 密码（MD5加密） |
| real_name | varchar(100) | NULL | 真实姓名 |
| grade | varchar(50) | NULL | 年级 |
| class_name | varchar(50) | NULL | 班级 |
| school | varchar(200) | NULL | 学校 |
| parent_name | varchar(100) | NULL | 家长姓名 |
| parent_phone | varchar(20) | NULL | 家长电话 |
| parent_wechat | varchar(100) | NULL | 家长微信 |
| emergency_contact | varchar(100) | NULL | 紧急联系人 |
| emergency_phone | varchar(20) | NULL | 紧急联系电话 |
| address | varchar(500) | NULL | 家庭地址 |
| notes | text | NULL | 备注信息 |
| status | tinyint(1) | NOT NULL, DEFAULT 1 | 状态：1-正常，0-停用 |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `uk_student_number` (student_number) - 唯一索引
- `uk_username` (username) - 唯一索引
- `uk_email` (email) - 唯一索引
- `idx_grade` (grade) - 普通索引
- `idx_class` (class_name) - 普通索引

**特殊设计**:
- 学生表同样包含完整的用户基本信息，与教师表保持一致的设计
- 字段名从`class`改为`class_name`，避免与SQL关键字冲突

### 4. 课程表 (edu_course)

**表说明**: 课程信息表，存储课程的基本信息和教学安排

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| name | varchar(200) | NOT NULL | 课程名称 |
| subject | varchar(100) | NOT NULL | 科目 |
| teacher_id | int(11) | NOT NULL | 授课教师ID |
| description | text | NULL | 课程描述 |
| grade_level | varchar(50) | NULL | 适用年级 |
| max_students | int(11) | DEFAULT 30 | 最大学生数 |
| price | decimal(10,2) | DEFAULT 0.00 | 课程价格 |
| duration_minutes | int(11) | DEFAULT 90 | 课程时长（分钟） |
| start_date | date | NULL | 开课日期 |
| end_date | date | NULL | 结课日期 |
| status | tinyint(1) | NOT NULL, DEFAULT 1 | 状态：1-正常，0-停用 |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `idx_teacher_id` (teacher_id) - 普通索引
- `idx_subject` (subject) - 普通索引
- `idx_status` (status) - 普通索引
- `idx_start_date` (start_date) - 普通索引

**关联关系**:
- `teacher_id` 关联 `edu_teacher.id`

### 5. 课程安排表 (edu_course_schedule)

**表说明**: 课程时间安排表，管理课程的具体上课时间

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| course_id | int(11) | NOT NULL | 课程ID |
| classroom_id | int(11) | NULL | 教室ID |
| day_of_week | tinyint(1) | NOT NULL | 星期几：1-7（周一到周日） |
| start_time | time | NOT NULL | 开始时间 |
| end_time | time | NOT NULL | 结束时间 |
| effective_date | date | NULL | 生效日期 |
| expire_date | date | NULL | 失效日期 |
| status | tinyint(1) | NOT NULL, DEFAULT 1 | 状态：1-正常，0-停用 |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `idx_course_id` (course_id) - 普通索引
- `idx_classroom_id` (classroom_id) - 普通索引
- `idx_day_time` (day_of_week, start_time) - 复合索引

**关联关系**:
- `course_id` 关联 `edu_course.id`
- `classroom_id` 关联 `edu_classroom.id`

### 6. 学生课程关联表 (edu_student_course)

**表说明**: 学生选课关联表，管理学生的选课信息

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| student_id | int(11) | NOT NULL | 学生ID |
| course_id | int(11) | NOT NULL | 课程ID |
| enroll_date | date | NOT NULL | 报名日期 |
| status | tinyint(1) | NOT NULL, DEFAULT 1 | 状态：1-正常，0-退课 |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `uk_student_course` (student_id, course_id) - 唯一索引
- `idx_student_id` (student_id) - 普通索引
- `idx_course_id` (course_id) - 普通索引

**关联关系**:
- `student_id` 关联 `edu_student.id`
- `course_id` 关联 `edu_course.id`

### 7. 请假申请表 (edu_leave_request)

**表说明**: 学生请假申请表，管理请假申请和审批流程

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| student_id | int(11) | NOT NULL | 学生ID |
| course_id | int(11) | NOT NULL | 课程ID |
| schedule_id | int(11) | NULL | 课程安排ID |
| leave_type | tinyint(1) | NOT NULL, DEFAULT 2 | 请假类型：1-病假，2-事假，3-其他 |
| start_date | date | NOT NULL | 请假开始日期 |
| end_date | date | NOT NULL | 请假结束日期 |
| reason | text | NOT NULL | 请假原因 |
| status | tinyint(1) | NOT NULL, DEFAULT 0 | 状态：0-待审批，1-已批准，2-已拒绝 |
| approved_by | int(11) | NULL | 审批人ID |
| approved_at | timestamp | NULL | 审批时间 |
| comment | text | NULL | 审批意见 |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `idx_student_id` (student_id) - 普通索引
- `idx_course_id` (course_id) - 普通索引
- `idx_status` (status) - 普通索引
- `idx_start_date` (start_date) - 普通索引

**关联关系**:
- `student_id` 关联 `edu_student.id`
- `course_id` 关联 `edu_course.id`
- `schedule_id` 关联 `edu_course_schedule.id`
- `approved_by` 关联 `edu_teacher.id`

### 8. 考勤记录表 (edu_attendance)

**表说明**: 学生考勤记录表，记录学生的出勤情况

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| student_id | int(11) | NOT NULL | 学生ID |
| course_id | int(11) | NOT NULL | 课程ID |
| schedule_id | int(11) | NULL | 课程安排ID |
| attendance_date | date | NOT NULL | 考勤日期 |
| status | tinyint(1) | NOT NULL, DEFAULT 1 | 考勤状态：0-缺席，1-出席，2-请假 |
| notes | text | NULL | 备注 |
| recorded_by | int(11) | NULL | 记录人ID |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `uk_student_course_date` (student_id, course_id, attendance_date) - 唯一索引
- `idx_course_id` (course_id) - 普通索引
- `idx_attendance_date` (attendance_date) - 普通索引
- `idx_status` (status) - 普通索引

**关联关系**:
- `student_id` 关联 `edu_student.id`
- `course_id` 关联 `edu_course.id`
- `schedule_id` 关联 `edu_course_schedule.id`
- `recorded_by` 关联 `edu_teacher.id`

### 9. 教室表 (edu_classroom)

**表说明**: 教室信息表，管理教学场地资源

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int(11) | PK, AUTO_INCREMENT | 主键ID |
| name | varchar(100) | NOT NULL, UNIQUE | 教室名称 |
| location | varchar(200) | NULL | 教室位置 |
| capacity | int(11) | DEFAULT 0 | 容纳人数 |
| equipment | text | NULL | 设备信息 |
| notes | text | NULL | 备注信息 |
| status | tinyint(1) | NOT NULL, DEFAULT 1 | 状态：1-可用，0-维护中 |
| created_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- `uk_name` (name) - 唯一索引
- `idx_status` (status) - 普通索引

## 关键数据关系

### 1. 用户身份系统

**设计特点**: 教培系统采用独立的用户身份系统，不依赖主系统的用户表。

```
edu_user_role (角色关联)
├── user_id → 虚拟用户ID
├── role_id → 角色类型
└── admin_type → 管理员权限层级

edu_teacher (教师信息)
├── user_id → 关联角色表
├── username, email, password → 完整用户信息
└── subject, title → 教师专业信息

edu_student (学生信息)
├── user_id → 关联角色表
├── username, email, password → 完整用户信息
└── grade, class_name → 学生学籍信息
```

**身份互斥机制**: 用户只能选择教师或学生身份之一，通过应用层逻辑确保身份唯一性。

### 2. 权限管理系统

**三级权限层级**:
1. **专职管理员** (`admin_type='full'`)
   - 最高权限，可以设置/取消任何教师的管理员权限
   - 可以设置保护状态，防止误操作
   - 系统默认设置一个受保护的专职管理员

2. **兼任管理员** (`admin_type='part'`)
   - 教师身份+部分管理员权限
   - 可以设置其他教师为兼任管理员
   - 不能修改受保护的用户

3. **普通用户** (`admin_type='none'`)
   - 只有基本角色权限
   - 无管理员操作权限

**保护机制**: `is_protected=1` 的用户不能被兼任管理员修改权限。

### 3. 课程教学关系

**核心关系链**:
```
edu_teacher (教师)
└── edu_course (课程) [teacher_id]
    ├── edu_course_schedule (课程安排) [course_id]
    ├── edu_student_course (学生选课) [course_id]
    ├── edu_leave_request (请假申请) [course_id]
    └── edu_attendance (考勤记录) [course_id]
```

**多对多关系**: 学生和课程通过 `edu_student_course` 表建立多对多关系。

### 4. 请假审批流程

**审批链路**:
```
edu_student (学生) → edu_leave_request (请假申请) → edu_teacher (教师审批)
                           ↓
                    edu_attendance (考勤记录)
```

**状态流转**:
- `status=0`: 待审批
- `status=1`: 已批准 → 自动生成考勤记录 (`status=2` 请假)
- `status=2`: 已拒绝

### 5. 时间冲突检测

**课程安排冲突检测**:
- 同一教师在同一时间段不能安排多门课程
- 同一教室在同一时间段不能安排多门课程
- 通过 `day_of_week + start_time + end_time` 组合检测

**索引优化**: `idx_day_time` 复合索引优化时间冲突查询性能。

## 数据完整性约束

### 1. 唯一性约束
- 用户名、邮箱在教师表和学生表中分别唯一
- 教师编号、学号全局唯一
- 学生每门课程只能选课一次
- 学生每天每门课程只能有一条考勤记录

### 2. 外键关系
虽然数据库层面未设置外键约束，但应用层严格维护以下关系：
- 课程必须关联有效的教师
- 学生选课必须关联有效的学生和课程
- 请假申请必须关联有效的学生和课程
- 考勤记录必须关联有效的学生和课程

### 3. 业务规则约束
- 课程结束日期必须大于开始日期
- 请假结束日期必须大于等于开始日期
- 考勤日期必须在课程有效期内
- 管理员权限设置必须符合层级规则

## 性能优化策略

### 1. 索引设计
- **复合索引**: `idx_day_time` 优化课程安排查询
- **唯一索引**: 确保数据唯一性的同时提升查询性能
- **状态索引**: 频繁按状态筛选的字段都建立索引

### 2. 查询优化
- 使用 `prepare()` 函数进行参数绑定，防止SQL注入
- 避免 `SELECT *`，只查询需要的字段
- 合理使用 `LIMIT` 进行分页查询

### 3. 数据归档
- 定期归档历史考勤记录
- 清理过期的课程安排数据
- 保留重要的审计日志

## 扩展性考虑

### 1. 水平扩展
- 可按学期或年度进行表分区
- 考勤记录表可按时间范围分表
- 支持读写分离架构

### 2. 功能扩展
- 预留 `notes` 字段支持业务扩展
- 时间戳字段支持审计追踪
- 状态字段支持业务流程扩展

### 3. 集成扩展
- 用户ID设计支持与主系统集成
- 支持微信登录等第三方认证
- 预留接口支持移动端应用

---

**文档版本**: v1.0  
**最后更新**: 2025-08-22  
**维护人员**: 特靠谱教培系统开发团队
