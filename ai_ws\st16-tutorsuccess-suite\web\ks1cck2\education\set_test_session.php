<?php
/**
 * 设置测试用户session
 * 仅限localhost访问，用于开发测试
 * 修订日期：2025-08-22
 */

// 检查是否为localhost访问
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1', 'localhost'])) {
    http_response_code(403);
    die('Access denied. This page is only accessible from localhost.');
}

// 引入必要的文件
require_once '../_lp.php';
require_once '../func.inc.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['role']) || !isset($input['action'])) {
    echo json_encode(['success' => false, 'message' => '参数错误']);
    exit;
}

$role = $input['role'];
$action = $input['action'];

if ($action !== 'set_test_user') {
    echo json_encode(['success' => false, 'message' => '无效的操作']);
    exit;
}

// 定义测试用户数据
$test_users = [
    'admin' => [
        'user_id' => 'test_admin_001',
        'username' => '测试管理员',
        'role' => 'admin',
        'wechat_name' => '管理员微信',
        'wechat_openid' => 'test_admin_openid',
        'email' => '<EMAIL>',
        'phone' => '***********',
        'public_account_name' => '测试公众号',
        'group_id' => 1
    ],
    'teacher' => [
        'user_id' => 'test_teacher_001',
        'username' => '测试教师',
        'role' => 'user',
        'wechat_name' => '教师微信',
        'wechat_openid' => 'test_teacher_openid',
        'email' => '<EMAIL>',
        'phone' => '***********',
        'public_account_name' => '测试公众号',
        'group_id' => 2
    ],
    'student' => [
        'user_id' => 'test_student_001',
        'username' => '测试学生',
        'role' => 'user',
        'wechat_name' => '学生微信',
        'wechat_openid' => 'test_student_openid',
        'email' => '<EMAIL>',
        'phone' => '***********',
        'public_account_name' => '测试公众号',
        'group_id' => 3
    ],
    'parent' => [
        'user_id' => 'test_parent_001',
        'username' => '测试家长',
        'role' => 'user',
        'wechat_name' => '家长微信',
        'wechat_openid' => 'test_parent_openid',
        'email' => '<EMAIL>',
        'phone' => '***********',
        'public_account_name' => '测试公众号',
        'group_id' => 4
    ]
];

// 检查角色是否有效
if (!isset($test_users[$role])) {
    echo json_encode(['success' => false, 'message' => '无效的角色']);
    exit;
}

$user_data = $test_users[$role];

try {
    // 使用类似 user_login_xp 的逻辑设置session
    $_SESSION[SESSION_KEY] = [
        'user' => [
            'id' => $user_data['user_id'], // 用户ID
            'username' => $user_data['username'], // 用户名
            'role' => $user_data['role'], // 用户角色（如'user'或'admin'）
            'wx_name' => $user_data['wechat_name'], // 微信用户名称
            'openid' => $user_data['wechat_openid'], // 微信OpenID
            'email' => $user_data['email'], // 用户邮箱
            'phone' => $user_data['phone'], // 用户手机号码
            'p_a_name' => $user_data['public_account_name'], // 公众号名称
            'group_id' => $user_data['group_id']
        ]
    ];
    
    // 设置开发模式标识
    $_SESSION['dev_mode'] = true;
    $_SESSION['test_role'] = $role;
    
    echo json_encode([
        'success' => true, 
        'message' => '模拟登录成功',
        'user' => $user_data,
        'session_key' => SESSION_KEY
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '设置session失败: ' . $e->getMessage()]);
}
?>
