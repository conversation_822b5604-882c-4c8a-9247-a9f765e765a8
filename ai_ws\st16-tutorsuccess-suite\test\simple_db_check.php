<?php
/**
 * 简单数据库检查脚本
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置HTTP_HOST避免警告
$_SERVER['HTTP_HOST'] = 'localhost';

echo "开始检查数据库...\n";

// 引入数据库连接文件
try {
    require_once dirname(__DIR__) . '/web/ks1cck2/_lp.php';
    echo "数据库连接成功\n";
} catch (Exception $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 定义教培系统表名
$tables = [
    'edu_teacher' => '教师表',
    'edu_student' => '学生表', 
    'edu_course' => '课程表',
    'edu_user_role' => '用户角色表',
    'edu_leave_request' => '请假申请表',
    'edu_course_schedule' => '课程安排表',
    'edu_student_course' => '学生课程关联表',
    'edu_classroom' => '教室表',
    'edu_attendance' => '考勤表'
];

foreach ($tables as $table => $description) {
    echo "\n=== $description ($table) ===\n";
    
    try {
        // 检查表是否存在
        $sql = "SHOW TABLES LIKE '$table'";
        $exists = get_var($sql);
        
        if ($exists) {
            echo "✅ 表存在\n";
            
            // 获取记录数
            $sql = "SELECT COUNT(*) as count FROM $table";
            $count = get_var($sql);
            echo "📊 记录数: $count\n";
            
            if ($count > 0) {
                // 显示前3条记录
                $sql = "SELECT * FROM $table LIMIT 3";
                $data = get_data($sql);
                if ($data) {
                    echo "📋 示例数据:\n";
                    foreach ($data as $index => $row) {
                        echo "  记录" . ($index + 1) . ": ";
                        $display_fields = [];
                        foreach ($row as $key => $value) {
                            if (in_array($key, ['id', 'username', 'real_name', 'name', 'email', 'subject', 'status'])) {
                                $display_fields[] = "$key=$value";
                            }
                        }
                        echo implode(', ', $display_fields) . "\n";
                    }
                }
            }
        } else {
            echo "❌ 表不存在\n";
        }
    } catch (Exception $e) {
        echo "❌ 错误: " . $e->getMessage() . "\n";
    }
}

echo "\n检查完成！\n";
?>
