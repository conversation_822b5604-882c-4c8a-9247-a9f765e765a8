<?php
/**
 * 为教师表和学生表添加用户基本信息字段
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始为教师表和学生表添加用户基本信息字段...\n";

// 引入必要的文件
require_once 'web/ks1cck2/_lp.php';
require_once 'web/ks1cck2/education/config/database.php';

echo "数据库连接成功\n";

// 为教师表添加字段
echo "\n为教师表添加用户字段...\n";

$teacher_fields = [
    "ADD COLUMN `username` varchar(50) NOT NULL COMMENT '用户名' AFTER `teacher_number`",
    "ADD COLUMN `email` varchar(100) NOT NULL COMMENT '邮箱' AFTER `username`",
    "ADD COLUMN `password` varchar(32) NOT NULL COMMENT '密码' AFTER `email`",
    "ADD COLUMN `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名' AFTER `password`",
    "ADD UNIQUE KEY `uk_username` (`username`)",
    "ADD UNIQUE KEY `uk_email` (`email`)"
];

foreach ($teacher_fields as $field) {
    try {
        $sql = "ALTER TABLE " . TABLE_TEACHER . " " . $field;
        echo "执行SQL: " . $sql . "\n";
        $result = run_sql($sql);
        if ($result !== false) {
            echo "✓ 成功\n";
        } else {
            echo "✗ 失败\n";
        }
    } catch (Exception $e) {
        echo "✗ 错误: " . $e->getMessage() . "\n";
    }
}

// 为学生表添加字段
echo "\n为学生表添加用户字段...\n";

$student_fields = [
    "ADD COLUMN `username` varchar(50) NOT NULL COMMENT '用户名' AFTER `student_number`",
    "ADD COLUMN `email` varchar(100) NOT NULL COMMENT '邮箱' AFTER `username`",
    "ADD COLUMN `password` varchar(32) NOT NULL COMMENT '密码' AFTER `email`",
    "ADD COLUMN `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名' AFTER `password`",
    "ADD UNIQUE KEY `uk_username` (`username`)",
    "ADD UNIQUE KEY `uk_email` (`email`)"
];

foreach ($student_fields as $field) {
    try {
        $sql = "ALTER TABLE " . TABLE_STUDENT . " " . $field;
        echo "执行SQL: " . $sql . "\n";
        $result = run_sql($sql);
        if ($result !== false) {
            echo "✓ 成功\n";
        } else {
            echo "✗ 失败\n";
        }
    } catch (Exception $e) {
        echo "✗ 错误: " . $e->getMessage() . "\n";
    }
}

echo "\n字段添加完成！\n";
?>
