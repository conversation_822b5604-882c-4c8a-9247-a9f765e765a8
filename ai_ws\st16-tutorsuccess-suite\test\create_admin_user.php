<?php
/**
 * 创建管理员用户测试脚本
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置HTTP_HOST避免警告
$_SERVER['HTTP_HOST'] = 'localhost';

echo "开始创建管理员用户...\n";

// 引入必要的文件
require_once dirname(__DIR__) . '/web/ks1cck2/_lp.php';
require_once dirname(__DIR__) . '/web/ks1cck2/education/config/database.php';

echo "数据库连接成功\n";

// 管理员信息
$admin_data = [
    'username' => 'admin',
    'email' => '<EMAIL>',
    'password' => '123456',
    'real_name' => '系统管理员',
    'subject' => '管理',
    'title' => '管理员',
    'status' => 1
];

try {
    // 检查是否已存在该邮箱的用户
    $teacher_table = TABLE_TEACHER;
    $sql = "SELECT id FROM $teacher_table WHERE email = ?s";
    $sql = prepare($sql, [$admin_data['email']]);
    $existing_user = get_var($sql);
    
    if ($existing_user) {
        echo "用户已存在，ID: $existing_user\n";
        $admin_id = $existing_user;
    } else {
        // 创建教师记录（管理员也是教师身份）
        $password_hash = md5(md5($admin_data['password']) . "chatgpt@2023");
        $teacher_number = 'T' . date('Ymd') . '001';
        
        $sql = "INSERT INTO $teacher_table 
                (user_id, teacher_number, username, email, password, real_name, subject, title, status, created_at) 
                VALUES (0, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?i, NOW())";
        $sql = prepare($sql, [
            $teacher_number,
            $admin_data['username'],
            $admin_data['email'],
            $password_hash,
            $admin_data['real_name'],
            $admin_data['subject'],
            $admin_data['title'],
            $admin_data['status']
        ]);
        
        $result = run_sql($sql);
        if ($result) {
            $admin_id = last_id();
            echo "创建教师记录成功，ID: $admin_id\n";
            
            // 更新user_id字段
            $sql = "UPDATE $teacher_table SET user_id = ?i WHERE id = ?i";
            $sql = prepare($sql, [$admin_id, $admin_id]);
            run_sql($sql);
            echo "更新user_id成功\n";
        } else {
            throw new Exception("创建教师记录失败");
        }
    }
    
    // 检查是否已有管理员角色
    $role_table = TABLE_USER_ROLE;
    $sql = "SELECT id FROM $role_table WHERE user_id = ?i AND role_id = 1";
    $sql = prepare($sql, [$admin_id]);
    $existing_role = get_var($sql);
    
    if ($existing_role) {
        echo "管理员角色已存在，ID: $existing_role\n";
    } else {
        // 添加管理员角色
        $sql = "INSERT INTO $role_table 
                (user_id, role_id, admin_type, is_protected, created_at) 
                VALUES (?i, 1, 'full', 1, NOW())";
        $sql = prepare($sql, [$admin_id]);
        
        $result = run_sql($sql);
        if ($result) {
            $role_id = last_id();
            echo "创建管理员角色成功，ID: $role_id\n";
        } else {
            throw new Exception("创建管理员角色失败");
        }
    }
    
    echo "\n管理员用户创建完成！\n";
    echo "登录信息：\n";
    echo "邮箱: " . $admin_data['email'] . "\n";
    echo "密码: " . $admin_data['password'] . "\n";
    echo "角色: 管理员\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n脚本执行完成！\n";
?>
