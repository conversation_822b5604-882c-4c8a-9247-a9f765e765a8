<?php
/**
 * 用户登录页面
 * 修订日期：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 引入必要的文件
require_once '../_lp.php';
require_once 'config/database.php';

// 开始会话
session_start();

// 如果已经登录，重定向到相应的主页
if (isset($_SESSION['user_id'])) {
    $role = $_SESSION['user_role'] ?? 'student';
    switch ($role) {
        case 'admin':
            header('Location: admin_dashboard.php');
            break;
        case 'teacher':
            header('Location: teacher_dashboard.php');
            break;
        case 'student':
            header('Location: student_dashboard.php');
            break;
        default:
            header('Location: student_dashboard.php');
    }
    exit;
}

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    $response = ['success' => false, 'message' => ''];
    
    if (empty($email) || empty($password)) {
        $response['message'] = '请输入邮箱和密码';
    } else {
        // 验证用户
        $user = authenticateUser($email, $password);
        if ($user) {
            // 登录成功，设置会话
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['real_name'] = $user['real_name'];
            $_SESSION['email'] = $user['email'];
            
            // 如果选择记住我，设置cookie
            if ($remember) {
                $token = generateRememberToken($user['user_id']);
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30天
            }
            
            $response['success'] = true;
            $response['message'] = '登录成功';
            
            // 根据角色重定向
            switch ($user['role']) {
                case 'admin':
                    $response['redirect'] = 'admin_dashboard.php';
                    break;
                case 'teacher':
                    $response['redirect'] = 'teacher_dashboard.php';
                    break;
                case 'student':
                    $response['redirect'] = 'student_dashboard.php';
                    break;
                default:
                    $response['redirect'] = 'student_dashboard.php';
            }
        } else {
            $response['message'] = '邮箱或密码错误';
        }
    }
    
    // 如果是AJAX请求，返回JSON
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // 如果登录成功且不是AJAX，直接重定向
    if ($response['success']) {
        header('Location: ' . $response['redirect']);
        exit;
    }
}

/**
 * 验证用户登录
 */
function authenticateUser($email, $password) {
    // 先在教师表中查找
    $teacher_table = TABLE_TEACHER;
    $sql = "SELECT id as user_id, username, email, password, real_name, 'teacher' as role 
            FROM $teacher_table 
            WHERE email = ?s AND status = 1";
    $sql = prepare($sql, [$email]);
    $user = get_line($sql);
    
    if ($user && md5(md5($password) . "chatgpt@2023") === $user['password']) {
        return $user;
    }
    
    // 在学生表中查找
    $student_table = TABLE_STUDENT;
    $sql = "SELECT id as user_id, username, email, password, real_name, 'student' as role 
            FROM $student_table 
            WHERE email = ?s AND status = 1";
    $sql = prepare($sql, [$email]);
    $user = get_line($sql);
    
    if ($user && md5(md5($password) . "chatgpt@2023") === $user['password']) {
        return $user;
    }
    
    // 检查是否是管理员（在用户角色表中查找）
    $role_table = TABLE_USER_ROLE;
    $sql = "SELECT ur.user_id, t.username, t.email, t.password, t.real_name, 'admin' as role
            FROM $role_table ur
            JOIN $teacher_table t ON ur.user_id = t.id
            WHERE t.email = ?s AND ur.role_id = 1 AND ur.admin_type != 'none' AND t.status = 1";
    $sql = prepare($sql, [$email]);
    $user = get_line($sql);
    
    if ($user && md5(md5($password) . "chatgpt@2023") === $user['password']) {
        return $user;
    }
    
    return false;
}

/**
 * 生成记住我令牌
 */
function generateRememberToken($user_id) {
    $token = bin2hex(random_bytes(32));
    // 这里应该将token存储到数据库中，简化处理
    return base64_encode($user_id . ':' . $token);
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特靠谱教培系统 - 登录</title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            margin: 20px;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        .login-header h2 {
            margin: 0;
            font-weight: 300;
        }
        .login-body {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            border: none;
            border-bottom: 2px solid #eee;
            border-radius: 0;
            padding: 10px 0;
            background: transparent;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            border-bottom-color: #667eea;
            box-shadow: none;
            background: transparent;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            width: 100%;
            font-weight: 500;
            transition: transform 0.3s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            color: white;
        }
        .btn-wechat {
            background: #1aad19;
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            width: 100%;
            font-weight: 500;
            margin-top: 15px;
            transition: transform 0.3s;
        }
        .btn-wechat:hover {
            transform: translateY(-2px);
            color: white;
            background: #179b16;
        }
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #eee;
        }
        .divider span {
            background: white;
            padding: 0 15px;
            color: #999;
        }
        .alert {
            margin-bottom: 20px;
            border-radius: 10px;
        }
        .demo-accounts {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 0.9rem;
        }
        .demo-accounts h6 {
            color: #6c757d;
            margin-bottom: 10px;
        }
        .demo-account {
            margin-bottom: 5px;
            cursor: pointer;
            color: #007bff;
        }
        .demo-account:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fa fa-graduation-cap"></i> 特靠谱教培系统</h2>
            <p class="mb-0">课外补习班管理平台</p>
        </div>
        <div class="login-body">
            <?php if (isset($response) && !$response['success']): ?>
                <div class="alert alert-danger">
                    <?php echo htmlspecialchars($response['message']); ?>
                </div>
            <?php endif; ?>
            
            <form id="loginForm" method="POST">
                <div class="form-group">
                    <input type="email" class="form-control" id="email" name="email" placeholder="邮箱地址" required>
                </div>
                <div class="form-group">
                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                </div>
                <div class="form-check mb-3">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">记住我</label>
                </div>
                <button type="submit" class="btn btn-login">
                    <i class="fa fa-sign-in"></i> 登录
                </button>
            </form>
            
            <div class="divider">
                <span>或</span>
            </div>
            
            <button class="btn btn-wechat" onclick="wechatLogin()">
                <i class="fa fa-wechat"></i> 微信登录
            </button>
            
            <!-- 演示账号 -->
            <div class="demo-accounts">
                <h6>演示账号（点击快速填入）：</h6>
                <div class="demo-account" onclick="fillDemo('<EMAIL>', '123456')">
                    教师账号: <EMAIL> / 123456
                </div>
                <div class="demo-account" onclick="fillDemo('<EMAIL>', '123456')">
                    管理员账号: <EMAIL> / 123456
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 填入演示账号
        function fillDemo(email, password) {
            $('#email').val(email);
            $('#password').val(password);
        }
        
        // 微信登录处理
        function wechatLogin() {
            alert('微信登录功能开发中...');
        }
    </script>
</body>
</html>
