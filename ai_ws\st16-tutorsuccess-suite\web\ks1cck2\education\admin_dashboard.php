<?php
/**
 * 管理员主页
 * 修订日期：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 引入必要的文件
require_once '../_lp.php';
require_once 'config/database.php';

// 开始会话
session_start();

// 检查登录状态
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// 获取统计数据
function getSystemStats() {
    $stats = [];
    
    // 教师数量
    $teacher_table = TABLE_TEACHER;
    $sql = "SELECT COUNT(*) FROM $teacher_table WHERE status = 1";
    $stats['teachers'] = get_var($sql) ?: 0;
    
    // 学生数量
    $student_table = TABLE_STUDENT;
    $sql = "SELECT COUNT(*) FROM $student_table WHERE status = 1";
    $stats['students'] = get_var($sql) ?: 0;
    
    // 课程数量
    $course_table = TABLE_COURSE;
    $sql = "SELECT COUNT(*) FROM $course_table WHERE status = 1";
    $stats['courses'] = get_var($sql) ?: 0;
    
    // 教室数量
    $classroom_table = TABLE_CLASSROOM;
    $sql = "SELECT COUNT(*) FROM $classroom_table WHERE status = 1";
    $stats['classrooms'] = get_var($sql) ?: 0;
    
    return $stats;
}

// 获取最近活动
function getRecentActivities() {
    $activities = [];
    
    // 最近添加的课程
    $course_table = TABLE_COURSE;
    $teacher_table = TABLE_TEACHER;
    $sql = "SELECT c.name as course_name, t.real_name as teacher_name, c.created_at
            FROM $course_table c
            LEFT JOIN $teacher_table t ON c.teacher_id = t.id
            WHERE c.status = 1
            ORDER BY c.created_at DESC
            LIMIT 5";
    $courses = get_data($sql);
    
    if ($courses) {
        foreach ($courses as $course) {
            $activities[] = [
                'type' => 'course',
                'title' => '新增课程：' . $course['course_name'],
                'description' => '教师：' . ($course['teacher_name'] ?: '未分配'),
                'time' => $course['created_at']
            ];
        }
    }
    
    return $activities;
}

$stats = getSystemStats();
$activities = getRecentActivities();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员主页 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .feature-card {
            text-align: center;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s;
            height: 100%;
        }
        .feature-card:hover {
            background-color: #f8f9fa;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #6f42c1;
        }
        .quick-stats {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .activity-item {
            border-left: 4px solid #6f42c1;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            border-radius: 0 10px 10px 0;
        }
        .activity-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_dashboard.php">主页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">用户管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">课程管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classrooms.php">教室管理</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> <?php echo htmlspecialchars($_SESSION['real_name'] ?: $_SESSION['username']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3">欢迎回来，<?php echo htmlspecialchars($_SESSION['real_name'] ?: $_SESSION['username']); ?>！</h1>
                <p class="text-muted">今天是 <?php echo date('Y年m月d日 星期' . ['日','一','二','三','四','五','六'][date('w')]); ?></p>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="quick-stats">
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['teachers']; ?></span>
                        <span class="stat-label">教师</span>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['students']; ?></span>
                        <span class="stat-label">学生</span>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['courses']; ?></span>
                        <span class="stat-label">课程</span>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $stats['classrooms']; ?></span>
                        <span class="stat-label">教室</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 功能快捷入口 -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-tachometer-alt"></i> 快捷功能</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 col-6 mb-3">
                                <div class="feature-card" onclick="location.href='users.php'">
                                    <i class="fa fa-users feature-icon"></i>
                                    <h6>用户管理</h6>
                                    <p class="text-muted small">管理教师和学生账户</p>
                                </div>
                            </div>
                            <div class="col-md-4 col-6 mb-3">
                                <div class="feature-card" onclick="location.href='courses.php'">
                                    <i class="fa fa-book feature-icon"></i>
                                    <h6>课程管理</h6>
                                    <p class="text-muted small">创建和管理课程</p>
                                </div>
                            </div>
                            <div class="col-md-4 col-6 mb-3">
                                <div class="feature-card" onclick="location.href='classrooms.php'">
                                    <i class="fa fa-building feature-icon"></i>
                                    <h6>教室管理</h6>
                                    <p class="text-muted small">管理教室资源</p>
                                </div>
                            </div>
                            <div class="col-md-4 col-6 mb-3">
                                <div class="feature-card" onclick="location.href='schedule.php'">
                                    <i class="fa fa-calendar feature-icon"></i>
                                    <h6>课表管理</h6>
                                    <p class="text-muted small">安排课程时间</p>
                                </div>
                            </div>
                            <div class="col-md-4 col-6 mb-3">
                                <div class="feature-card" onclick="location.href='leave_requests.php'">
                                    <i class="fa fa-file-text feature-icon"></i>
                                    <h6>请假管理</h6>
                                    <p class="text-muted small">处理请假申请</p>
                                </div>
                            </div>
                            <div class="col-md-4 col-6 mb-3">
                                <div class="feature-card" onclick="location.href='reports.php'">
                                    <i class="fa fa-chart-bar feature-icon"></i>
                                    <h6>统计报表</h6>
                                    <p class="text-muted small">查看系统统计</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-clock"></i> 最近活动</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activities)): ?>
                            <p class="text-muted text-center">暂无最近活动</p>
                        <?php else: ?>
                            <?php foreach ($activities as $activity): ?>
                                <div class="activity-item">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                    <p class="mb-1 text-muted small"><?php echo htmlspecialchars($activity['description']); ?></p>
                                    <div class="activity-time"><?php echo date('m-d H:i', strtotime($activity['time'])); ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
</body>
</html>
