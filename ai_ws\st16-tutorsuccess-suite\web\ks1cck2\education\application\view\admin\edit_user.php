<?php
/**
 * 编辑用户页面
 * 修订日期：2025-08-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

$page_title = '编辑用户 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .form-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 8px;
        }
        .role-specific {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="?path=admin">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fa fa-user"></i> 
                    <?php echo isset($current_user['username']) ? $current_user['username'] : '管理员'; ?>
                </span>
                <a class="nav-link" href="../../../logout.php">
                    <i class="fa fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="?path=admin" style="color: white;">管理员控制台</a></li>
                <li class="breadcrumb-item"><a href="?path=admin/users" style="color: white;">用户管理</a></li>
                <li class="breadcrumb-item active" style="color: white;">编辑用户</li>
            </ol>
        </nav>

        <!-- 编辑用户表单 -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fa fa-user-edit"></i> 编辑用户</h4>
                <a href="?path=admin/users" class="btn btn-outline-secondary">
                    <i class="fa fa-arrow-left"></i> 返回用户列表
                </a>
            </div>

            <form id="editUserForm" method="POST">
                <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                
                <!-- 基本信息 -->
                <div class="form-section">
                    <h6 class="section-title"><i class="fa fa-info-circle"></i> 基本信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="real_name" class="form-label">真实姓名</label>
                                <input type="text" class="form-control" id="real_name" name="real_name" value="<?php echo htmlspecialchars($user['real_name']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">用户角色</label>
                                <input type="hidden" name="role" value="<?php echo $user['role']; ?>">
                                <input type="text" class="form-control" value="<?php echo $user['role'] === 'teacher' ? '教师' : ($user['role'] === 'student' ? '学生' : '管理员'); ?>" readonly>
                                <div class="form-text">用户角色创建后不可修改</div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($user['role'] === 'teacher'): ?>
                <!-- 教师信息 -->
                <div class="form-section">
                    <h6 class="section-title"><i class="fa fa-chalkboard-teacher"></i> 教师信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="teacher_number" class="form-label">教师编号</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['teacher_number']); ?>" readonly>
                                <div class="form-text">教师编号系统自动生成</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subject" class="form-label">主教科目</label>
                                <input type="text" class="form-control" id="subject" name="subject" value="<?php echo htmlspecialchars($user['teacher_subject']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">职称</label>
                                <select class="form-select" id="title" name="title">
                                    <option value="">请选择职称</option>
                                    <option value="初级教师" <?php echo $user['teacher_title'] === '初级教师' ? 'selected' : ''; ?>>初级教师</option>
                                    <option value="中级教师" <?php echo $user['teacher_title'] === '中级教师' ? 'selected' : ''; ?>>中级教师</option>
                                    <option value="高级教师" <?php echo $user['teacher_title'] === '高级教师' ? 'selected' : ''; ?>>高级教师</option>
                                    <option value="特级教师" <?php echo $user['teacher_title'] === '特级教师' ? 'selected' : ''; ?>>特级教师</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($user['role'] === 'student'): ?>
                <!-- 学生信息 -->
                <div class="form-section">
                    <h6 class="section-title"><i class="fa fa-user-graduate"></i> 学生信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="student_number" class="form-label">学生编号</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['student_number']); ?>" readonly>
                                <div class="form-text">学生编号系统自动生成</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grade" class="form-label">年级</label>
                                <select class="form-select" id="grade" name="grade">
                                    <option value="">请选择年级</option>
                                    <option value="小学一年级" <?php echo $user['grade'] === '小学一年级' ? 'selected' : ''; ?>>小学一年级</option>
                                    <option value="小学二年级" <?php echo $user['grade'] === '小学二年级' ? 'selected' : ''; ?>>小学二年级</option>
                                    <option value="小学三年级" <?php echo $user['grade'] === '小学三年级' ? 'selected' : ''; ?>>小学三年级</option>
                                    <option value="小学四年级" <?php echo $user['grade'] === '小学四年级' ? 'selected' : ''; ?>>小学四年级</option>
                                    <option value="小学五年级" <?php echo $user['grade'] === '小学五年级' ? 'selected' : ''; ?>>小学五年级</option>
                                    <option value="小学六年级" <?php echo $user['grade'] === '小学六年级' ? 'selected' : ''; ?>>小学六年级</option>
                                    <option value="初一" <?php echo $user['grade'] === '初一' ? 'selected' : ''; ?>>初一</option>
                                    <option value="初二" <?php echo $user['grade'] === '初二' ? 'selected' : ''; ?>>初二</option>
                                    <option value="初三" <?php echo $user['grade'] === '初三' ? 'selected' : ''; ?>>初三</option>
                                    <option value="高一" <?php echo $user['grade'] === '高一' ? 'selected' : ''; ?>>高一</option>
                                    <option value="高二" <?php echo $user['grade'] === '高二' ? 'selected' : ''; ?>>高二</option>
                                    <option value="高三" <?php echo $user['grade'] === '高三' ? 'selected' : ''; ?>>高三</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="class_name" class="form-label">班级</label>
                                <input type="text" class="form-control" id="class_name" name="class_name" value="<?php echo htmlspecialchars($user['class_name']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_name" class="form-label">家长姓名</label>
                                <input type="text" class="form-control" id="parent_name" name="parent_name" value="<?php echo htmlspecialchars($user['parent_name']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_phone" class="form-label">家长电话</label>
                                <input type="tel" class="form-control" id="parent_phone" name="parent_phone" value="<?php echo htmlspecialchars($user['parent_phone']); ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 提交按钮 -->
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                        <i class="fa fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-save"></i> 保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 表单提交处理
        document.getElementById('editUserForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // 显示加载状态
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 保存中...';
            submitBtn.disabled = true;
            
            fetch('?path=admin/editUser', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('用户信息更新成功！');
                    window.location.href = '?path=admin/users';
                } else {
                    alert('错误：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新用户信息时发生错误，请稍后重试');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
