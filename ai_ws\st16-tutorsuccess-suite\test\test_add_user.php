<?php
/**
 * 测试添加用户功能
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "测试添加用户功能...\n";

// 引入必要的文件
require_once 'web/ks1cck2/_lp.php';
require_once 'web/ks1cck2/education/config/database.php';

// 模拟POST数据
$_POST = [
    'username' => 'testteacher2',
    'email' => '<EMAIL>',
    'password' => '123456',
    'real_name' => '测试教师2',
    'role' => 'teacher',
    'subject' => '计算机',
    'title' => '中级教师'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "模拟POST数据设置完成\n";

// 模拟登录状态
define('IN_LOGIN', true);

try {
    // 直接测试数据库操作
    $username = 'testteacher2';
    $email = '<EMAIL>';
    $password = '123456';
    $real_name = '测试教师2';
    $role = 'teacher';
    $subject = '计算机';
    $title = '中级教师';

    echo "开始测试数据库操作...\n";

    // 检查用户名是否已存在
    $teacher_table = TABLE_TEACHER;
    $student_table = TABLE_STUDENT;

    echo "教师表名: $teacher_table\n";
    echo "学生表名: $student_table\n";

    // 检查教师表中是否有相同的用户名
    $sql = "SELECT id FROM $teacher_table WHERE username = ?s";
    $sql = prepare($sql, [$username]);
    echo "检查教师表SQL: $sql\n";
    $existing_teacher = get_var($sql);
    echo "现有教师: " . ($existing_teacher ? $existing_teacher : '无') . "\n";

    // 检查学生表中是否有相同的用户名
    $sql = "SELECT id FROM $student_table WHERE username = ?s";
    $sql = prepare($sql, [$username]);
    echo "检查学生表SQL: $sql\n";
    $existing_student = get_var($sql);
    echo "现有学生: " . ($existing_student ? $existing_student : '无') . "\n";

    if ($existing_teacher || $existing_student) {
        throw new Exception('用户名已存在');
    }

    // 生成虚拟用户ID
    $user_id = time();
    echo "生成用户ID: $user_id\n";

    // 创建教师记录
    $teacher_number = 'T' . $username;

    $sql = "INSERT INTO $teacher_table
            (user_id, teacher_number, username, email, password, real_name, subject, title, status, created_at, updated_at)
            VALUES (?i, ?s, ?s, ?s, ?s, ?s, ?s, ?s, ?i, NOW(), NOW())";
    $sql = prepare($sql, [$user_id, $teacher_number, $username, $email, md5($password), $real_name, $subject, $title, STATUS_ACTIVE]);

    echo "插入教师SQL: $sql\n";

    $result = run_sql($sql);

    if ($result) {
        echo "✓ 教师创建成功！\n";
    } else {
        echo "✗ 教师创建失败！\n";
    }

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

echo "\n测试完成！\n";
?>
