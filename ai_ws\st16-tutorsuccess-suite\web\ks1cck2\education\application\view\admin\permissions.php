<?php
/**
 * 权限管理页面
 * 修订日期：2025-08-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

$page_title = '权限管理 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .permission-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        .permission-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .admin-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        .admin-full {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        .admin-part {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }
        .admin-none {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
        }
        .protected-badge {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="?path=admin">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fa fa-user"></i> 
                    <?php echo isset($current_user['username']) ? $current_user['username'] : '管理员'; ?>
                    <?php if ($current_admin['admin_type'] === 'full'): ?>
                        <span class="badge admin-full ms-1">专职管理员</span>
                    <?php elseif ($current_admin['admin_type'] === 'part'): ?>
                        <span class="badge admin-part ms-1">兼任管理员</span>
                    <?php endif; ?>
                </span>
                <a class="nav-link" href="../../../logout.php">
                    <i class="fa fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="?path=admin" style="color: white;">管理员控制台</a></li>
                <li class="breadcrumb-item active" style="color: white;">权限管理</li>
            </ol>
        </nav>

        <!-- 权限管理内容 -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fa fa-shield-alt"></i> 权限管理</h4>
                <div>
                    <span class="badge admin-full me-2">专职管理员</span>
                    <span class="badge admin-part me-2">兼任管理员</span>
                    <span class="badge admin-none me-2">普通教师</span>
                    <span class="badge protected-badge">受保护</span>
                </div>
            </div>

            <!-- 权限说明 -->
            <div class="alert alert-info mb-4">
                <h6><i class="fa fa-info-circle"></i> 权限说明</h6>
                <ul class="mb-0">
                    <li><strong>专职管理员</strong>：拥有最高权限，可以设置/取消任何教师的管理员权限，可以设置保护状态</li>
                    <li><strong>兼任管理员</strong>：教师身份+部分管理员权限，可以设置其他教师为兼任管理员</li>
                    <li><strong>普通教师</strong>：只有教师权限，无管理员操作权限</li>
                    <li><strong>受保护状态</strong>：不能被兼任管理员修改权限，只有专职管理员可以操作</li>
                </ul>
            </div>

            <!-- 教师权限列表 -->
            <div class="row">
                <?php if (!empty($teachers)): ?>
                    <?php foreach ($teachers as $teacher): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="permission-card">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($teacher['real_name'] ?: $teacher['username']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($teacher['username']); ?></small>
                                    </div>
                                    <div class="text-end">
                                        <?php if ($teacher['admin_type'] === 'full'): ?>
                                            <span class="badge admin-full admin-badge">专职管理员</span>
                                        <?php elseif ($teacher['admin_type'] === 'part'): ?>
                                            <span class="badge admin-part admin-badge">兼任管理员</span>
                                        <?php else: ?>
                                            <span class="badge admin-none admin-badge">普通教师</span>
                                        <?php endif; ?>
                                        
                                        <?php if ($teacher['is_protected']): ?>
                                            <br><span class="badge protected-badge admin-badge mt-1">受保护</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fa fa-book"></i> <?php echo htmlspecialchars($teacher['subject']); ?>
                                        <?php if ($teacher['title']): ?>
                                            | <i class="fa fa-award"></i> <?php echo htmlspecialchars($teacher['title']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>

                                <!-- 权限操作按钮 -->
                                <?php if ($current_admin['admin_type'] === 'full' || 
                                         ($current_admin['admin_type'] === 'part' && !$teacher['is_protected'])): ?>
                                    <div class="btn-group w-100" role="group">
                                        <?php if ($current_admin['admin_type'] === 'full'): ?>
                                            <!-- 专职管理员可以进行所有操作 -->
                                            <?php if ($teacher['admin_type'] !== 'none'): ?>
                                                <button class="btn btn-outline-secondary btn-sm" 
                                                        onclick="setPermission(<?php echo $teacher['user_id']; ?>, 'none', false)">
                                                    取消权限
                                                </button>
                                            <?php endif; ?>
                                            
                                            <?php if ($teacher['admin_type'] !== 'part'): ?>
                                                <button class="btn btn-outline-warning btn-sm" 
                                                        onclick="setPermission(<?php echo $teacher['user_id']; ?>, 'part', false)">
                                                    设为兼任
                                                </button>
                                            <?php endif; ?>
                                            
                                            <?php if (!$teacher['is_protected'] && $teacher['admin_type'] !== 'none'): ?>
                                                <button class="btn btn-outline-info btn-sm" 
                                                        onclick="setPermission(<?php echo $teacher['user_id']; ?>, '<?php echo $teacher['admin_type']; ?>', true)">
                                                    设为保护
                                                </button>
                                            <?php elseif ($teacher['is_protected']): ?>
                                                <button class="btn btn-outline-danger btn-sm" 
                                                        onclick="setPermission(<?php echo $teacher['user_id']; ?>, '<?php echo $teacher['admin_type']; ?>', false)">
                                                    取消保护
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <!-- 兼任管理员只能设置为兼任管理员 -->
                                            <?php if ($teacher['admin_type'] === 'none'): ?>
                                                <button class="btn btn-outline-warning btn-sm w-100" 
                                                        onclick="setPermission(<?php echo $teacher['user_id']; ?>, 'part', false)">
                                                    设为兼任管理员
                                                </button>
                                            <?php else: ?>
                                                <small class="text-muted">已有管理员权限</small>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <small class="text-muted">权限不足或用户受保护</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fa fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">暂无教师数据</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function setPermission(userId, adminType, isProtected) {
            let actionText = '';
            switch (adminType) {
                case 'none': actionText = '取消管理员权限'; break;
                case 'part': actionText = '设置为兼任管理员'; break;
                case 'full': actionText = '设置为专职管理员'; break;
            }
            
            if (isProtected) {
                actionText += '并设为受保护状态';
            }
            
            if (!confirm('确定要' + actionText + '吗？')) {
                return;
            }
            
            const formData = new FormData();
            formData.append('user_id', userId);
            formData.append('admin_type', adminType);
            if (isProtected) {
                formData.append('is_protected', '1');
            }
            
            fetch('?path=admin/setAdminPermission', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('权限设置成功！');
                    location.reload();
                } else {
                    alert('错误：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('设置权限时发生错误，请稍后重试');
            });
        }
    </script>
</body>
</html>
