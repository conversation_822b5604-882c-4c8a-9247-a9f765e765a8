<?php
/**
 * 教育系统登录测试页面
 * 创建时间：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 定义常量避免令牌检查
define('IN_LOGIN', true);

// 引入必要的文件
require_once __DIR__ . '/ks1cck2/_lp.php';
require_once __DIR__ . '/ks1cck2/education/config/database.php';

// 开始会话
session_start();

// 处理登录请求
$login_result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $login_result = ['success' => false, 'message' => '请输入邮箱和密码'];
    } else {
        // 验证用户
        $user = authenticateUser($email, $password);
        if ($user) {
            // 登录成功，设置会话
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['real_name'] = $user['real_name'];
            $_SESSION['email'] = $user['email'];
            
            $login_result = [
                'success' => true, 
                'message' => '登录成功',
                'user' => $user
            ];
        } else {
            $login_result = ['success' => false, 'message' => '邮箱或密码错误'];
        }
    }
}

// 处理退出登录
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

/**
 * 验证用户登录
 */
function authenticateUser($email, $password) {
    // 先在教师表中查找
    $teacher_table = TABLE_TEACHER;
    $sql = "SELECT id as user_id, username, email, password, real_name, 'teacher' as role 
            FROM $teacher_table 
            WHERE email = ?s AND status = 1";
    $sql = prepare($sql, [$email]);
    $user = get_line($sql);
    
    if ($user && md5(md5($password) . "chatgpt@2023") === $user['password']) {
        return $user;
    }
    
    // 在学生表中查找
    $student_table = TABLE_STUDENT;
    $sql = "SELECT id as user_id, username, email, password, real_name, 'student' as role 
            FROM $student_table 
            WHERE email = ?s AND status = 1";
    $sql = prepare($sql, [$email]);
    $user = get_line($sql);
    
    if ($user && md5(md5($password) . "chatgpt@2023") === $user['password']) {
        return $user;
    }
    
    // 检查是否是管理员（在用户角色表中查找）
    $role_table = TABLE_USER_ROLE;
    $sql = "SELECT ur.user_id, t.username, t.email, t.password, t.real_name, 'admin' as role
            FROM $role_table ur
            JOIN $teacher_table t ON ur.user_id = t.id
            WHERE t.email = ?s AND ur.role_id = 1 AND ur.admin_type != 'none' AND t.status = 1";
    $sql = prepare($sql, [$email]);
    $user = get_line($sql);
    
    if ($user && md5(md5($password) . "chatgpt@2023") === $user['password']) {
        return $user;
    }
    
    return false;
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特靠谱教培系统 - 登录测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .login-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            opacity: 0.9;
            color: white;
            text-decoration: none;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .demo-accounts {
            background: #e9ecef;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .demo-account {
            margin: 5px 0;
            padding: 5px 10px;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            border: 1px solid #ddd;
        }
        .demo-account:hover {
            background: #f0f0f0;
        }
        .user-info {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #bee5eb;
        }
        .session-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ffeaa7;
            margin-top: 20px;
        }
        .dashboard-links {
            background: #d4edda;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .dashboard-links h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        .dashboard-links a {
            margin: 5px 10px 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 特靠谱教培系统 - 登录测试</h1>
            <p>测试时间：<?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <?php if ($login_result): ?>
            <?php if ($login_result['success']): ?>
                <div class="alert alert-success">
                    <h4>✅ 登录成功！</h4>
                    <div class="user-info">
                        <h5>用户信息：</h5>
                        <p><strong>用户ID：</strong><?php echo htmlspecialchars($login_result['user']['user_id']); ?></p>
                        <p><strong>用户名：</strong><?php echo htmlspecialchars($login_result['user']['username']); ?></p>
                        <p><strong>真实姓名：</strong><?php echo htmlspecialchars($login_result['user']['real_name']); ?></p>
                        <p><strong>邮箱：</strong><?php echo htmlspecialchars($login_result['user']['email']); ?></p>
                        <p><strong>角色：</strong><?php echo htmlspecialchars($login_result['user']['role']); ?></p>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <h4>❌ 登录失败</h4>
                    <p><?php echo htmlspecialchars($login_result['message']); ?></p>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <div class="login-form">
            <h3>🔐 登录测试</h3>
            <form method="POST">
                <input type="hidden" name="token" value="<?php echo defined('TOKEN') ? TOKEN : ''; ?>">
                <div class="form-group">
                    <label for="email">邮箱：</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">密码：</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn">🔑 登录测试</button>
            </form>
        </div>

        <div class="demo-accounts">
            <h4>👥 测试账号（点击快速填入）：</h4>
            <div class="demo-account" onclick="fillDemo('<EMAIL>', '123456')">
                <strong>👨‍💼 管理员：</strong><EMAIL> / 123456
            </div>
            <div class="demo-account" onclick="fillDemo('<EMAIL>', '123456')">
                <strong>👨‍🏫 教师：</strong><EMAIL> / 123456
            </div>
            <div class="demo-account" onclick="fillDemo('<EMAIL>', '123456')">
                <strong>👦 学生1：</strong><EMAIL> / 123456 (张小明)
            </div>
            <div class="demo-account" onclick="fillDemo('<EMAIL>', '123456')">
                <strong>👧 学生2：</strong><EMAIL> / 123456 (李小红)
            </div>
            <div class="demo-account" onclick="fillDemo('<EMAIL>', '123456')">
                <strong>👦 学生3：</strong><EMAIL> / 123456 (王小强)
            </div>
        </div>

        <?php if (isset($_SESSION['user_id'])): ?>
        <div class="session-info">
            <h4>👤 当前会话信息：</h4>
            <p><strong>用户ID：</strong><?php echo htmlspecialchars($_SESSION['user_id']); ?></p>
            <p><strong>用户角色：</strong><?php echo htmlspecialchars($_SESSION['user_role']); ?></p>
            <p><strong>用户名：</strong><?php echo htmlspecialchars($_SESSION['username']); ?></p>
            <p><strong>真实姓名：</strong><?php echo htmlspecialchars($_SESSION['real_name']); ?></p>
            <p><strong>邮箱：</strong><?php echo htmlspecialchars($_SESSION['email']); ?></p>
            <a href="?logout=1" class="btn" style="background: #dc3545;">🚪 退出登录</a>
        </div>
        
        <div class="dashboard-links">
            <h4>🏠 访问对应的主页：</h4>
            <?php if ($_SESSION['user_role'] === 'admin'): ?>
                <a href="ks1cck2/education/admin_test.php" class="btn" target="_blank">⚙️ 管理员主页</a>
            <?php elseif ($_SESSION['user_role'] === 'teacher'): ?>
                <a href="ks1cck2/education/teacher_test.php" class="btn" target="_blank">👨‍🏫 教师主页</a>
            <?php elseif ($_SESSION['user_role'] === 'student'): ?>
                <a href="ks1cck2/education/student_test.php" class="btn" target="_blank">🎓 学生主页</a>
            <?php endif; ?>
            <a href="ks1cck2/education/index.php" class="btn" target="_blank">🏠 系统入口</a>
            <a href="ks1cck2/education/login.php" class="btn" target="_blank">🔐 正式登录页</a>
        </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p><a href="index.php" style="color: #667eea;">← 返回主站</a></p>
        </div>
    </div>

    <script>
        function fillDemo(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
    </script>
</body>
</html>
