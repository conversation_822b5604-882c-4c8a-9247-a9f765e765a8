<?php
/**
 * 特靠谱教培系统入口文件
 * 修订日期：2025-01-22
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义登录标识，允许访问init.php
define('IN_LOGIN', true);

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

// 定义根目录
define('ROOT_PATH', __DIR__ . '/../');

// 引入基础配置
require_once ROOT_PATH . '../_ks1.php';
require_once ROOT_PATH . '../mysqlconn.php';
require_once ROOT_PATH . 'init.php';

// 简单的路由处理
class Router
{
    private $routes = [];
    
    public function __construct()
    {
        // 定义路由规则
        $this->routes = [
            'teacher' => 'Teacher@index',
            'teacher/schedule' => 'Teacher@schedule',
            'teacher/leave' => 'Teacher@leave',
            'teacher/approve' => 'Teacher@approveLeave',
            'teacher/start' => 'Teacher@startClass',
            'student' => 'Student@index',
            'student/schedule' => 'Student@schedule',
            'student/leave' => 'Student@leave',
            'parent' => 'ParentUser@index',
            'parent/schedule' => 'ParentUser@schedule',
            'parent/leaves' => 'ParentUser@leaves',
            'parent/children' => 'ParentUser@children',
            'admin' => 'Admin@index',
            'admin/users' => 'Admin@users',
            'admin/courses' => 'Admin@courses',
            'admin/statistics' => 'Admin@statistics',
            'admin/settings' => 'Admin@settings',
            'admin/permissions' => 'Admin@permissions',
            'admin/addUser' => 'Admin@addUser',
            'admin/editUser' => 'Admin@editUser',
            'admin/deleteUser' => 'Admin@deleteUser',
            'admin/setAdminPermission' => 'Admin@setAdminPermission',
            '' => 'Index@index'
        ];
    }
    
    public function dispatch()
    {
        // 优先从 GET 参数获取路径
        if (isset($_GET['path'])) {
            $path = $_GET['path'];
        } else {
            // 获取请求路径
            $request_uri = $_SERVER['REQUEST_URI'];

            // 获取基础路径（去掉域名和端口）
            $base_path = '/ks1cck2/education/';

            // 移除基础路径
            if (strpos($request_uri, $base_path) === 0) {
                $path = substr($request_uri, strlen($base_path));
            } else {
                $path = $request_uri;
            }

            // 移除查询字符串
            if (($pos = strpos($path, '?')) !== false) {
                $path = substr($path, 0, $pos);
            }
        }

        // 清理路径
        $path = trim($path, '/');

        // 如果路径为空，使用默认路由
        if (empty($path)) {
            $path = '';
        }



        // 查找路由
        if (isset($this->routes[$path])) {
            $route = $this->routes[$path];
        } else {
            // 尝试匹配部分路由
            $found = false;
            foreach ($this->routes as $pattern => $target) {
                if (strpos($path, $pattern) === 0) {
                    $route = $target;
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                // 默认路由
                $route = $this->routes[''];
            }
        }

        // 解析控制器和方法
        list($controller, $method) = explode('@', $route);

        // 加载控制器
        $controller_file = APP_PATH . 'controller/' . $controller . '.php';
        if (!file_exists($controller_file)) {
            // 调试信息
            error_log("控制器文件不存在: " . $controller_file);
            $this->show404();
            return;
        }

        require_once $controller_file;

        $controller_class = 'app\\controller\\' . $controller;
        if (!class_exists($controller_class)) {
            // 调试信息
            error_log("控制器类不存在: " . $controller_class);
            $this->show404();
            return;
        }

        $controller_instance = new $controller_class();
        if (!method_exists($controller_instance, $method)) {
            $this->show404();
            return;
        }

        // 执行方法
        $controller_instance->$method();
    }
    
    private function show404()
    {
        http_response_code(404);
        echo '404 Not Found';
    }
}

// 启动应用
try {
    $router = new Router();
    $router->dispatch();
} catch (Exception $e) {
    error_log('教培系统错误: ' . $e->getMessage());
    echo '系统错误，请稍后重试';
}
