<?php
/**
 * 检查学生表结构
 * 创建时间：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 引入必要的文件
require_once 'web/ks1cck2/_lp.php';
require_once 'web/ks1cck2/education/config/database.php';

echo "<h2>学生表结构检查</h2>\n";
echo "<p>检查时间：" . date('Y-m-d H:i:s') . "</p>\n";

// 检查学生表结构
echo "<h3>edu_student 表结构：</h3>\n";
$sql = "DESCRIBE edu_student";
$result = get_data($sql);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>\n";
    foreach ($result as $field) {
        echo "<tr>";
        echo "<td>{$field['Field']}</td>";
        echo "<td>{$field['Type']}</td>";
        echo "<td>{$field['Null']}</td>";
        echo "<td>{$field['Key']}</td>";
        echo "<td>{$field['Default']}</td>";
        echo "<td>{$field['Extra']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p style='color: red;'>❌ 无法获取表结构</p>\n";
}

// 尝试插入一个简单的学生记录
echo "<h3>尝试插入测试学生：</h3>\n";

$test_student = [
    'username' => 'teststudent',
    'email' => '<EMAIL>',
    'password' => md5(md5('123456') . "chatgpt@2023"),
    'real_name' => '测试学生',
    'phone' => '13800138000',
    'status' => 1
];

// 检查必需字段
$required_fields = ['username', 'email', 'password', 'real_name', 'status'];
$insert_fields = [];
$insert_values = [];
$insert_params = [];

foreach ($required_fields as $field) {
    if (isset($test_student[$field])) {
        $insert_fields[] = $field;
        $insert_values[] = '?s';
        $insert_params[] = $test_student[$field];
    }
}

// 添加时间字段
$insert_fields[] = 'created_at';
$insert_fields[] = 'updated_at';
$insert_values[] = 'NOW()';
$insert_values[] = 'NOW()';

$sql = "INSERT INTO edu_student (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_values) . ")";
echo "<p>准备执行的SQL：</p>\n";
echo "<pre>$sql</pre>\n";
echo "<p>参数：" . implode(', ', $insert_params) . "</p>\n";

// 先检查邮箱是否已存在
$existing = get_var("SELECT COUNT(*) FROM edu_student WHERE email = ?s", [$test_student['email']]);
if ($existing > 0) {
    echo "<p style='color: orange;'>⚠️ 邮箱已存在，先删除旧记录</p>\n";
    $delete_sql = "DELETE FROM edu_student WHERE email = ?s";
    $delete_sql = prepare($delete_sql, [$test_student['email']]);
    run_sql($delete_sql);
}

// 执行插入
$sql = prepare($sql, $insert_params);
echo "<p>最终SQL：</p>\n";
echo "<pre>$sql</pre>\n";

$result = run_sql($sql);
if ($result) {
    echo "<p style='color: green;'>✅ 插入成功</p>\n";
    
    // 查询插入的记录
    $inserted = get_line("SELECT * FROM edu_student WHERE email = ?s", [$test_student['email']]);
    if ($inserted) {
        echo "<h4>插入的记录：</h4>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        foreach ($inserted as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>\n";
        }
        echo "</table>\n";
    }
} else {
    echo "<p style='color: red;'>❌ 插入失败</p>\n";
    
    // 尝试获取错误信息
    global $conn;
    if (isset($conn) && is_object($conn) && property_exists($conn, 'error')) {
        echo "<p>MySQL错误：" . $conn->error . "</p>\n";
    }
}

echo "<hr>\n";
echo "<p>检查完成时间：" . date('Y-m-d H:i:s') . "</p>\n";
?>
