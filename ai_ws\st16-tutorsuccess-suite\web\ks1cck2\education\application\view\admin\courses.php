<?php
/**
 * 管理员课程管理页面
 * 修订日期：2025-08-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

$page_title = '课程管理 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .course-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        .course-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .course-status {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .btn-action {
            margin: 0 2px;
            padding: 5px 10px;
            font-size: 12px;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .course-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="?path=admin">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fa fa-user"></i> 
                    <?php echo isset($current_user['username']) ? $current_user['username'] : '管理员'; ?>
                </span>
                <a class="nav-link" href="../../../logout.php">
                    <i class="fa fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="?path=admin" style="color: white;">管理员控制台</a></li>
                <li class="breadcrumb-item active" style="color: white;">课程管理</li>
            </ol>
        </nav>

        <!-- 课程管理内容 -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fa fa-book"></i> 课程管理</h4>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                    <i class="fa fa-plus"></i> 添加课程
                </button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-box">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" placeholder="搜索课程名称..." id="searchInput">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="categoryFilter">
                            <option value="">所有分类</option>
                            <option value="math">数学</option>
                            <option value="english">英语</option>
                            <option value="physics">物理</option>
                            <option value="chemistry">化学</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">所有状态</option>
                            <option value="active">进行中</option>
                            <option value="completed">已完成</option>
                            <option value="suspended">已暂停</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="searchCourses()">
                            <i class="fa fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 课程列表 -->
            <div class="row" id="coursesList">
                <?php if (isset($courses) && !empty($courses)): ?>
                    <?php foreach ($courses as $course): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="course-card position-relative">
                                <span class="badge bg-<?php echo $course['status'] === 'active' ? 'success' : ($course['status'] === 'completed' ? 'secondary' : 'warning'); ?> course-status">
                                    <?php 
                                    echo $course['status'] === 'active' ? '进行中' : 
                                         ($course['status'] === 'completed' ? '已完成' : '已暂停'); 
                                    ?>
                                </span>
                                
                                <div class="course-image">
                                    <i class="fa fa-book"></i>
                                </div>
                                
                                <h6><?php echo htmlspecialchars($course['name']); ?></h6>
                                <p class="text-muted small"><?php echo htmlspecialchars($course['description']); ?></p>
                                
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <small class="text-muted">教师</small><br>
                                        <strong><?php echo htmlspecialchars($course['teacher']); ?></strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">学生</small><br>
                                        <strong><?php echo $course['students']; ?>人</strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">课时</small><br>
                                        <strong><?php echo $course['hours']; ?>h</strong>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm btn-outline-primary" onclick="editCourse(<?php echo $course['id']; ?>)">
                                        <i class="fa fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="viewCourse(<?php echo $course['id']; ?>)">
                                        <i class="fa fa-eye"></i> 详情
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCourse(<?php echo $course['id']; ?>)">
                                        <i class="fa fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- 示例数据 -->
                    <div class="col-md-6 col-lg-4">
                        <div class="course-card position-relative">
                            <span class="badge bg-success course-status">进行中</span>
                            <div class="course-image">
                                <i class="fa fa-calculator"></i>
                            </div>
                            <h6>高中数学</h6>
                            <p class="text-muted small">高中数学基础课程，包含代数、几何、概率统计等内容</p>
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <small class="text-muted">教师</small><br>
                                    <strong>张老师</strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">学生</small><br>
                                    <strong>25人</strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">课时</small><br>
                                    <strong>40h</strong>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="editCourse(1)">
                                    <i class="fa fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewCourse(1)">
                                    <i class="fa fa-eye"></i> 详情
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCourse(1)">
                                    <i class="fa fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-4">
                        <div class="course-card position-relative">
                            <span class="badge bg-success course-status">进行中</span>
                            <div class="course-image">
                                <i class="fa fa-language"></i>
                            </div>
                            <h6>初中英语</h6>
                            <p class="text-muted small">初中英语综合课程，提升听说读写能力</p>
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <small class="text-muted">教师</small><br>
                                    <strong>李老师</strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">学生</small><br>
                                    <strong>18人</strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">课时</small><br>
                                    <strong>30h</strong>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="editCourse(2)">
                                    <i class="fa fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewCourse(2)">
                                    <i class="fa fa-eye"></i> 详情
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCourse(2)">
                                    <i class="fa fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-4">
                        <div class="course-card position-relative">
                            <span class="badge bg-warning course-status">已暂停</span>
                            <div class="course-image">
                                <i class="fa fa-atom"></i>
                            </div>
                            <h6>高中物理</h6>
                            <p class="text-muted small">高中物理课程，涵盖力学、电学、光学等内容</p>
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <small class="text-muted">教师</small><br>
                                    <strong>王老师</strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">学生</small><br>
                                    <strong>15人</strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">课时</small><br>
                                    <strong>35h</strong>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="editCourse(3)">
                                    <i class="fa fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewCourse(3)">
                                    <i class="fa fa-eye"></i> 详情
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCourse(3)">
                                    <i class="fa fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- 分页 -->
            <nav aria-label="课程列表分页">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">上一页</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function searchCourses() {
            alert('搜索功能开发中...');
        }
        
        function editCourse(courseId) {
            alert('编辑课程 ID: ' + courseId + ' 功能开发中...');
        }
        
        function viewCourse(courseId) {
            alert('查看课程 ID: ' + courseId + ' 详情功能开发中...');
        }
        
        function deleteCourse(courseId) {
            if (confirm('确定要删除这个课程吗？')) {
                alert('删除课程 ID: ' + courseId + ' 功能开发中...');
            }
        }
    </script>
</body>
</html>
