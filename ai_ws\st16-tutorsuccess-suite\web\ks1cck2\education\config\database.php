<?php
/**
 * 教培系统数据库配置文件
 * 创建时间：2025-01-22
 */

// 教培系统数据库表前缀
define('EDU_TABLE_PREFIX', 'edu_');

// 教培系统数据库表名常量
define('TABLE_TEACHER', EDU_TABLE_PREFIX . 'teacher');
define('TABLE_STUDENT', EDU_TABLE_PREFIX . 'student');
define('TABLE_CLASSROOM', EDU_TABLE_PREFIX . 'classroom');
define('TABLE_COURSE', EDU_TABLE_PREFIX . 'course');
define('TABLE_COURSE_SCHEDULE', EDU_TABLE_PREFIX . 'course_schedule');
define('TABLE_STUDENT_COURSE', EDU_TABLE_PREFIX . 'student_course');
define('TABLE_LEAVE_REQUEST', EDU_TABLE_PREFIX . 'leave_request');
define('TABLE_ATTENDANCE', EDU_TABLE_PREFIX . 'attendance');
define('TABLE_USER_ROLE', EDU_TABLE_PREFIX . 'user_role');

// 角色类型常量
define('ROLE_ADMIN', 1);
define('ROLE_TEACHER', 2);
define('ROLE_STUDENT', 3);

// 请假类型常量
define('LEAVE_TYPE_SICK', 1);      // 病假
define('LEAVE_TYPE_PERSONAL', 2);  // 事假
define('LEAVE_TYPE_OTHER', 3);     // 其他

// 请假状态常量
define('LEAVE_STATUS_PENDING', 0);   // 待审批
define('LEAVE_STATUS_APPROVED', 1);  // 已批准
define('LEAVE_STATUS_REJECTED', 2);  // 已拒绝

// 考勤状态常量
define('ATTENDANCE_ABSENT', 0);    // 缺席
define('ATTENDANCE_PRESENT', 1);   // 出席
define('ATTENDANCE_LEAVE', 2);     // 请假

// 状态常量
define('STATUS_ACTIVE', 1);        // 正常/可用
define('STATUS_INACTIVE', 0);      // 停用/维护中

// 管理员类型常量
define('ADMIN_TYPE_NONE', 'none');      // 无管理员权限
define('ADMIN_TYPE_FULL', 'full');      // 专职管理员
define('ADMIN_TYPE_PART', 'part');      // 兼任管理员

/**
 * 获取教培系统表名
 * @param string $table 表名（不含前缀）
 * @return string 完整表名
 */
function get_edu_table($table) {
    return EDU_TABLE_PREFIX . $table;
}

/**
 * 初始化教培系统数据库表
 * @return bool 是否成功
 */
function init_edu_database() {
    global $conn;
    
    $sql_file = __DIR__ . '/../sql/education_system.sql';
    if (!file_exists($sql_file)) {
        return false;
    }
    
    $sql = file_get_contents($sql_file);
    $queries = explode(';', $sql);
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query)) {
            if (!$conn->query($query)) {
                error_log("教培系统数据库初始化失败: " . $conn->error);
                return false;
            }
        }
    }
    
    return true;
}

/**
 * 检查教培系统数据库表是否存在
 * @return bool 是否存在
 */
function check_edu_tables() {
    global $conn;
    
    $tables = [
        TABLE_TEACHER,
        TABLE_STUDENT,
        TABLE_CLASSROOM,
        TABLE_COURSE,
        TABLE_COURSE_SCHEDULE,
        TABLE_STUDENT_COURSE,
        TABLE_LEAVE_REQUEST,
        TABLE_ATTENDANCE,
        TABLE_USER_ROLE
    ];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows == 0) {
            return false;
        }
    }
    
    return true;
}
