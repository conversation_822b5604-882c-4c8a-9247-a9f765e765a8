<?php
/**
 * 管理员用户管理页面
 * 修订日期：2025-08-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

$page_title = '用户管理 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .user-table {
            margin-top: 20px;
        }
        .btn-action {
            margin: 0 2px;
            padding: 5px 10px;
            font-size: 12px;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="?path=admin">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fa fa-user"></i> 
                    <?php echo isset($current_user['username']) ? $current_user['username'] : '管理员'; ?>
                </span>
                <a class="nav-link" href="../../../logout.php">
                    <i class="fa fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="?path=admin" style="color: white;">管理员控制台</a></li>
                <li class="breadcrumb-item active" style="color: white;">用户管理</li>
            </ol>
        </nav>

        <!-- 用户管理内容 -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4><i class="fa fa-users"></i> 用户管理</h4>
                <a href="?path=admin/addUser" class="btn btn-primary">
                    <i class="fa fa-plus"></i> 添加用户
                </a>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-box">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" placeholder="搜索用户名、邮箱..." id="searchInput">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="roleFilter">
                            <option value="">所有角色</option>
                            <option value="admin">管理员</option>
                            <option value="teacher">教师</option>
                            <option value="student">学生</option>
                            <option value="parent">家长</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">所有状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">停用</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="searchUsers()">
                            <i class="fa fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="user-table">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>头像</th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>身份</th>
                            <th>管理员权限</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <?php if (isset($users) && !empty($users)): ?>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="user-avatar">
                                            <?php echo mb_substr($user['username'], 0, 1); ?>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'teacher' ? 'warning' : 'info'); ?>">
                                            <?php echo $user['role_name']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (isset($user['admin_type'])): ?>
                                            <?php if ($user['admin_type'] === 'full'): ?>
                                                <span class="badge bg-danger">专职管理员</span>
                                                <?php if ($user['is_protected']): ?>
                                                    <br><span class="badge bg-info mt-1">受保护</span>
                                                <?php endif; ?>
                                            <?php elseif ($user['admin_type'] === 'part'): ?>
                                                <span class="badge bg-warning text-dark">兼任管理员</span>
                                                <?php if ($user['is_protected']): ?>
                                                    <br><span class="badge bg-info mt-1">受保护</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">无</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">无</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $user['status'] === 'active' ? '活跃' : '停用'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $user['created_at']; ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="editUser(<?php echo $user['id']; ?>)">
                                            <i class="fa fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteUser(<?php echo $user['id']; ?>)">
                                            <i class="fa fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <!-- 示例数据 -->
                            <tr>
                                <td><div class="user-avatar">张</div></td>
                                <td>张三</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-warning">教师</span></td>
                                <td><span class="badge bg-success">活跃</span></td>
                                <td>2025-01-15 10:30:00</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary btn-action" onclick="editUser(1)">
                                        <i class="fa fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteUser(1)">
                                        <i class="fa fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><div class="user-avatar">李</div></td>
                                <td>李四</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-info">学生</span></td>
                                <td><span class="badge bg-success">活跃</span></td>
                                <td>2025-01-14 14:20:00</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary btn-action" onclick="editUser(2)">
                                        <i class="fa fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteUser(2)">
                                        <i class="fa fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><div class="user-avatar">王</div></td>
                                <td>王五</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-info">学生</span></td>
                                <td><span class="badge bg-secondary">停用</span></td>
                                <td>2025-01-13 09:15:00</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary btn-action" onclick="editUser(3)">
                                        <i class="fa fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteUser(3)">
                                        <i class="fa fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <nav aria-label="用户列表分页">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">上一页</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function searchUsers() {
            // 实现搜索功能
            alert('搜索功能开发中...');
        }
        
        function editUser(userId) {
            // 跳转到编辑用户页面
            window.location.href = '?path=admin/editUser&id=' + userId;
        }

        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可撤销！')) {
                // 发送删除请求
                const formData = new FormData();
                formData.append('id', userId);

                fetch('?path=admin/deleteUser', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('用户删除成功！');
                        location.reload(); // 刷新页面
                    } else {
                        alert('错误：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除用户时发生错误，请稍后重试');
                });
            }
        }
    </script>
</body>
</html>
