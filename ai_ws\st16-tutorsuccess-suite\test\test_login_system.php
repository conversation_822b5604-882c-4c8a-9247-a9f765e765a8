<?php
/**
 * 测试登录系统状态
 * 创建时间：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 引入必要的文件
require_once dirname(__DIR__) . '/web/ks1cck2/_lp.php';
require_once dirname(__DIR__) . '/web/ks1cck2/education/config/database.php';

echo "<h2>特靠谱教培系统 - 登录系统测试</h2>\n";
echo "<p>测试时间：" . date('Y-m-d H:i:s') . "</p>\n";

// 1. 检查数据库连接
echo "<h3>1. 数据库连接测试</h3>\n";
try {
    $test_sql = "SELECT 1 as test";
    $result = get_var($test_sql);
    if ($result == 1) {
        echo "<p style='color: green;'>✅ 数据库连接正常</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 数据库连接异常</p>\n";
        exit;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败：" . $e->getMessage() . "</p>\n";
    exit;
}

// 2. 检查教培系统表是否存在
echo "<h3>2. 教培系统表检查</h3>\n";
$tables = [
    'edu_teacher' => '教师表',
    'edu_student' => '学生表', 
    'edu_classroom' => '教室表',
    'edu_course' => '课程表',
    'edu_course_schedule' => '课程安排表',
    'edu_student_course' => '学生课程关联表',
    'edu_leave_request' => '请假申请表',
    'edu_attendance' => '考勤表',
    'edu_user_role' => '用户角色表'
];

$missing_tables = [];
foreach ($tables as $table => $desc) {
    $sql = "SHOW TABLES LIKE '$table'";
    $result = get_var($sql);
    if ($result) {
        echo "<p style='color: green;'>✅ $desc ($table) 存在</p>\n";
    } else {
        echo "<p style='color: red;'>❌ $desc ($table) 不存在</p>\n";
        $missing_tables[] = $table;
    }
}

// 3. 检查测试用户数据
echo "<h3>3. 测试用户数据检查</h3>\n";

// 检查教师数据
$teacher_count = get_var("SELECT COUNT(*) FROM edu_teacher");
echo "<p>教师数量：$teacher_count</p>\n";

if ($teacher_count > 0) {
    $teachers = get_data("SELECT id, username, email, real_name, status FROM edu_teacher LIMIT 5");
    echo "<h4>教师列表（前5条）：</h4>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>真实姓名</th><th>状态</th></tr>\n";
    foreach ($teachers as $teacher) {
        $status = $teacher['status'] ? '正常' : '禁用';
        echo "<tr><td>{$teacher['id']}</td><td>{$teacher['username']}</td><td>{$teacher['email']}</td><td>{$teacher['real_name']}</td><td>$status</td></tr>\n";
    }
    echo "</table>\n";
}

// 检查学生数据
$student_count = get_var("SELECT COUNT(*) FROM edu_student");
echo "<p>学生数量：$student_count</p>\n";

if ($student_count > 0) {
    $students = get_data("SELECT id, username, email, real_name, status FROM edu_student LIMIT 5");
    echo "<h4>学生列表（前5条）：</h4>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>真实姓名</th><th>状态</th></tr>\n";
    foreach ($students as $student) {
        $status = $student['status'] ? '正常' : '禁用';
        echo "<tr><td>{$student['id']}</td><td>{$student['username']}</td><td>{$student['email']}</td><td>{$student['real_name']}</td><td>$status</td></tr>\n";
    }
    echo "</table>\n";
}

// 检查管理员数据
$admin_count = get_var("SELECT COUNT(*) FROM edu_user_role WHERE role_id = 1 AND admin_type != 'none'");
echo "<p>管理员数量：$admin_count</p>\n";

if ($admin_count > 0) {
    $admins = get_data("
        SELECT ur.user_id, t.username, t.email, t.real_name, ur.admin_type
        FROM edu_user_role ur
        JOIN edu_teacher t ON ur.user_id = t.id
        WHERE ur.role_id = 1 AND ur.admin_type != 'none'
        LIMIT 5
    ");
    echo "<h4>管理员列表：</h4>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>用户ID</th><th>用户名</th><th>邮箱</th><th>真实姓名</th><th>管理员类型</th></tr>\n";
    foreach ($admins as $admin) {
        echo "<tr><td>{$admin['user_id']}</td><td>{$admin['username']}</td><td>{$admin['email']}</td><td>{$admin['real_name']}</td><td>{$admin['admin_type']}</td></tr>\n";
    }
    echo "</table>\n";
}

// 4. 测试登录验证函数
echo "<h3>4. 登录验证函数测试</h3>\n";

// 测试教师登录
if ($teacher_count > 0) {
    $test_teacher = get_line("SELECT email FROM edu_teacher WHERE status = 1 LIMIT 1");
    if ($test_teacher) {
        echo "<p>测试教师邮箱：{$test_teacher['email']}</p>\n";
        echo "<p style='color: orange;'>⚠️ 需要通过浏览器测试实际登录功能</p>\n";
    }
}

// 5. 生成测试数据（如果需要）
if ($teacher_count == 0 || $student_count == 0) {
    echo "<h3>5. 生成测试数据</h3>\n";
    echo "<p style='color: orange;'>⚠️ 检测到缺少测试数据，建议运行 insert_test_data.php 生成测试数据</p>\n";
    echo "<p><a href='insert_test_data.php' target='_blank'>点击生成测试数据</a></p>\n";
}

// 6. 系统状态总结
echo "<h3>6. 系统状态总结</h3>\n";
if (empty($missing_tables) && $teacher_count > 0 && $student_count > 0) {
    echo "<p style='color: green; font-weight: bold;'>✅ 系统基础数据完整，可以进行登录测试</p>\n";
    echo "<p><a href='../web/ks1cck2/education/login.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>打开登录页面测试</a></p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ 系统数据不完整，需要先初始化数据</p>\n";
    if (!empty($missing_tables)) {
        echo "<p>缺少表：" . implode(', ', $missing_tables) . "</p>\n";
    }
    if ($teacher_count == 0) {
        echo "<p>缺少教师测试数据</p>\n";
    }
    if ($student_count == 0) {
        echo "<p>缺少学生测试数据</p>\n";
    }
}

echo "<hr>\n";
echo "<p>测试完成时间：" . date('Y-m-d H:i:s') . "</p>\n";
?>
