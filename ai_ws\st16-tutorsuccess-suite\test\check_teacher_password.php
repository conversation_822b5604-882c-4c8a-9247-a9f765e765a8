<?php
/**
 * 检查教师密码
 * 创建时间：2025-08-22
 */

// 设置HTTP_HOST避免警告
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost';
}

// 引入必要的文件
require_once dirname(__DIR__) . '/web/ks1cck2/_lp.php';
require_once dirname(__DIR__) . '/web/ks1cck2/education/config/database.php';

echo "检查教师密码\n";
echo "时间：" . date('Y-m-d H:i:s') . "\n\n";

// 检查***********************
$email = '<EMAIL>';
$sql = "SELECT id, username, email, real_name, password FROM edu_teacher WHERE email = ?s";
$sql = prepare($sql, [$email]);
$user = get_line($sql);

if ($user) {
    echo "找到教师：\n";
    echo "ID: " . $user['id'] . "\n";
    echo "用户名: " . $user['username'] . "\n";
    echo "邮箱: " . $user['email'] . "\n";
    echo "真实姓名: " . $user['real_name'] . "\n";
    echo "密码哈希: " . $user['password'] . "\n";
    
    $expected_hash = md5(md5('123456') . "chatgpt@2023");
    echo "期望哈希: " . $expected_hash . "\n";
    echo "密码匹配: " . ($user['password'] === $expected_hash ? '是' : '否') . "\n";
    
    // 如果密码不匹配，更新密码
    if ($user['password'] !== $expected_hash) {
        echo "\n更新教师密码...\n";
        $update_sql = "UPDATE edu_teacher SET password = ?s WHERE id = ?i";
        $update_sql = prepare($update_sql, [$expected_hash, $user['id']]);
        $result = run_sql($update_sql);
        if ($result) {
            echo "密码更新成功\n";
        } else {
            echo "密码更新失败\n";
        }
    }
} else {
    echo "未找到教师: $email\n";
    
    // 显示所有教师
    echo "\n所有教师列表：\n";
    $all_teachers = get_data("SELECT id, username, email, real_name FROM edu_teacher");
    foreach ($all_teachers as $teacher) {
        echo "ID: {$teacher['id']}, 用户名: {$teacher['username']}, 邮箱: {$teacher['email']}, 姓名: {$teacher['real_name']}\n";
    }
}

echo "\n检查完成\n";
?>
