<?php
/**
 * 插入测试数据
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始插入测试数据...\n";

// 引入必要的文件
require_once 'web/ks1cck2/_lp.php';
require_once 'web/ks1cck2/education/config/database.php';

echo "数据库连接成功\n";

// 插入课程数据
echo "\n插入课程数据...\n";

$courses = [
    [
        'name' => '高中数学基础',
        'description' => '高中数学基础课程，包含代数、几何、概率统计等内容',
        'subject' => '数学',
        'teacher_id' => 1,
        'grade_level' => '高中',
        'start_date' => '2025-01-01',
        'end_date' => '2025-06-30',
        'duration_minutes' => 2400, // 40小时
        'max_students' => 30,
        'price' => 2000.00,
        'status' => STATUS_ACTIVE
    ],
    [
        'name' => '初中英语综合',
        'description' => '初中英语综合课程，提升听说读写能力',
        'subject' => '英语',
        'teacher_id' => 2,
        'grade_level' => '初中',
        'start_date' => '2025-02-01',
        'end_date' => '2025-07-31',
        'duration_minutes' => 1800, // 30小时
        'max_students' => 25,
        'price' => 1500.00,
        'status' => STATUS_ACTIVE
    ],
    [
        'name' => '高中物理实验',
        'description' => '高中物理课程，涵盖力学、电学、光学等内容',
        'subject' => '物理',
        'teacher_id' => 1,
        'grade_level' => '高中',
        'start_date' => '2025-01-15',
        'end_date' => '2025-05-15',
        'duration_minutes' => 2100, // 35小时
        'max_students' => 20,
        'price' => 1800.00,
        'status' => STATUS_INACTIVE // 暂停状态
    ]
];

foreach ($courses as $course) {
    try {
        $sql = "INSERT INTO " . TABLE_COURSE . "
                (name, description, subject, teacher_id, grade_level, start_date, end_date,
                 duration_minutes, max_students, price, status, created_at, updated_at)
                VALUES (?s, ?s, ?s, ?i, ?s, ?s, ?s, ?i, ?i, ?s, ?i, NOW(), NOW())";

        $sql = prepare($sql, [
            $course['name'],
            $course['description'],
            $course['subject'],
            $course['teacher_id'],
            $course['grade_level'],
            $course['start_date'],
            $course['end_date'],
            $course['duration_minutes'],
            $course['max_students'],
            (string)$course['price'], // 转换为字符串
            $course['status']
        ]);
        
        echo "执行SQL: " . $sql . "\n";
        $result = run_sql($sql);
        if ($result) {
            echo "✓ 插入课程: " . $course['name'] . "\n";
        } else {
            echo "✗ 插入课程失败: " . $course['name'] . "\n";
            // 获取MySQL错误信息
            global $conn;
            if ($conn && $conn->error) {
                echo "MySQL错误: " . $conn->error . "\n";
            }
        }
    } catch (Exception $e) {
        echo "✗ 插入课程错误: " . $course['name'] . " - " . $e->getMessage() . "\n";
    }
}

// 插入学生数据
echo "\n插入学生数据...\n";

$students = [
    [
        'user_id' => 4,
        'student_number' => 'S001',
        'grade' => '高一',
        'class_name' => '高一(1)班',
        'parent_name' => '张父',
        'parent_phone' => '13800138001',
        'status' => STATUS_ACTIVE
    ],
    [
        'user_id' => 5,
        'student_number' => 'S002',
        'grade' => '初三',
        'class_name' => '初三(2)班',
        'parent_name' => '李父',
        'parent_phone' => '13800138002',
        'status' => STATUS_ACTIVE
    ]
];

foreach ($students as $student) {
    try {
        $sql = "INSERT INTO " . TABLE_STUDENT . " 
                (user_id, student_number, grade, class_name, parent_name, parent_phone, status, created_at, updated_at) 
                VALUES (?i, ?s, ?s, ?s, ?s, ?s, ?i, NOW(), NOW())";
        
        $sql = prepare($sql, [
            $student['user_id'],
            $student['student_number'],
            $student['grade'],
            $student['class_name'],
            $student['parent_name'],
            $student['parent_phone'],
            $student['status']
        ]);
        
        $result = run_sql($sql);
        if ($result) {
            echo "✓ 插入学生: " . $student['student_number'] . "\n";
        } else {
            echo "✗ 插入学生失败: " . $student['student_number'] . "\n";
        }
    } catch (Exception $e) {
        echo "✗ 插入学生错误: " . $student['student_number'] . " - " . $e->getMessage() . "\n";
    }
}

// 插入学生课程关联数据
echo "\n插入学生课程关联数据...\n";

$student_courses = [
    ['student_id' => 1, 'course_id' => 1], // 学生1选择课程1
    ['student_id' => 1, 'course_id' => 3], // 学生1选择课程3
    ['student_id' => 2, 'course_id' => 2], // 学生2选择课程2
];

foreach ($student_courses as $sc) {
    try {
        $sql = "INSERT INTO " . TABLE_STUDENT_COURSE . " 
                (student_id, course_id, enrollment_date, status, created_at, updated_at) 
                VALUES (?i, ?i, NOW(), ?i, NOW(), NOW())";
        
        $sql = prepare($sql, [
            $sc['student_id'],
            $sc['course_id'],
            STATUS_ACTIVE
        ]);
        
        $result = run_sql($sql);
        if ($result) {
            echo "✓ 插入学生课程关联: 学生{$sc['student_id']} -> 课程{$sc['course_id']}\n";
        } else {
            echo "✗ 插入学生课程关联失败\n";
        }
    } catch (Exception $e) {
        echo "✗ 插入学生课程关联错误: " . $e->getMessage() . "\n";
    }
}

echo "\n测试数据插入完成！\n";
?>
