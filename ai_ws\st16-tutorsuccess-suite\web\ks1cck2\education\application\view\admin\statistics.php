<?php
/**
 * 管理员数据统计页面
 * 修订日期：2025-08-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

$page_title = '数据统计 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        .progress-item {
            margin-bottom: 15px;
        }
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="?path=admin">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fa fa-user"></i> 
                    <?php echo isset($current_user['username']) ? $current_user['username'] : '管理员'; ?>
                </span>
                <a class="nav-link" href="../../../logout.php">
                    <i class="fa fa-sign-out-alt"></i> 退出
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="?path=admin" style="color: white;">管理员控制台</a></li>
                <li class="breadcrumb-item active" style="color: white;">数据统计</li>
            </ol>
        </nav>

        <!-- 统计概览 -->
        <div class="content-card">
            <h4><i class="fa fa-chart-bar"></i> 数据统计概览</h4>
            
            <!-- 用户统计 -->
            <h6 class="mt-4 mb-3">用户统计</h6>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['user_stats']['total_users']; ?></div>
                        <div class="stat-label">总用户数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['user_stats']['new_users_this_month']; ?></div>
                        <div class="stat-label">本月新增</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['user_stats']['active_users']; ?></div>
                        <div class="stat-label">活跃用户</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['user_stats']['inactive_users']; ?></div>
                        <div class="stat-label">非活跃用户</div>
                    </div>
                </div>
            </div>

            <!-- 课程统计 -->
            <h6 class="mt-4 mb-3">课程统计</h6>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['course_stats']['total_courses']; ?></div>
                        <div class="stat-label">总课程数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['course_stats']['active_courses']; ?></div>
                        <div class="stat-label">进行中</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['course_stats']['completed_courses']; ?></div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['course_stats']['suspended_courses']; ?></div>
                        <div class="stat-label">已暂停</div>
                    </div>
                </div>
            </div>

            <!-- 财务统计 -->
            <h6 class="mt-4 mb-3">财务统计</h6>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">¥<?php echo number_format($stats['financial_stats']['total_revenue']); ?></div>
                        <div class="stat-label">总收入</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">¥<?php echo number_format($stats['financial_stats']['this_month_revenue']); ?></div>
                        <div class="stat-label">本月收入</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">¥<?php echo number_format($stats['financial_stats']['pending_payments']); ?></div>
                        <div class="stat-label">待收款</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">¥<?php echo number_format($stats['financial_stats']['refunds']); ?></div>
                        <div class="stat-label">退款金额</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表分析 -->
        <div class="row">
            <div class="col-md-6">
                <div class="content-card">
                    <h6><i class="fa fa-pie-chart"></i> 用户分布</h6>
                    <div class="chart-container">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="content-card">
                    <h6><i class="fa fa-bar-chart"></i> 课程状态</h6>
                    <div class="chart-container">
                        <canvas id="courseChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活动统计 -->
        <div class="content-card">
            <h6><i class="fa fa-activity"></i> 活动统计</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>总课时</span>
                            <span><?php echo $stats['activity_stats']['total_lessons']; ?></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-primary" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>已完成课时</span>
                            <span><?php echo $stats['activity_stats']['completed_lessons']; ?></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?php echo ($stats['activity_stats']['completed_lessons'] / $stats['activity_stats']['total_lessons']) * 100; ?>%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>待审批请假</span>
                            <span><?php echo $stats['activity_stats']['pending_leaves']; ?></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 20%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>已批准请假</span>
                            <span><?php echo $stats['activity_stats']['approved_leaves']; ?></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: 80%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 用户分布饼图
        const userCtx = document.getElementById('userChart').getContext('2d');
        new Chart(userCtx, {
            type: 'pie',
            data: {
                labels: ['活跃用户', '非活跃用户'],
                datasets: [{
                    data: [<?php echo $stats['user_stats']['active_users']; ?>, <?php echo $stats['user_stats']['inactive_users']; ?>],
                    backgroundColor: ['#28a745', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // 课程状态柱状图
        const courseCtx = document.getElementById('courseChart').getContext('2d');
        new Chart(courseCtx, {
            type: 'bar',
            data: {
                labels: ['进行中', '已完成', '已暂停'],
                datasets: [{
                    label: '课程数量',
                    data: [
                        <?php echo $stats['course_stats']['active_courses']; ?>,
                        <?php echo $stats['course_stats']['completed_courses']; ?>,
                        <?php echo $stats['course_stats']['suspended_courses']; ?>
                    ],
                    backgroundColor: ['#28a745', '#6c757d', '#ffc107']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
