<?php
/**
 * 管理员端测试页面
 * 修订日期：2025-08-22
 */

// 引入基础配置
require_once '../_ks1.php';
require_once '../mysqlconn.php';

// 模拟数据
$current_user = ['id' => 1, 'name' => '系统管理员'];
$stats = [
    'total_users' => 156,
    'total_teachers' => 12,
    'total_students' => 134,
    'total_courses' => 25,
    'active_courses' => 18,
    'pending_leaves' => 8,
    'this_month_revenue' => 45600,
    'last_month_revenue' => 42300
];

$recent_activities = [
    [
        'type' => 'user_register',
        'message' => '新用户 张三 注册成功',
        'time' => '2025-08-22 15:30',
        'icon' => 'user-plus'
    ],
    [
        'type' => 'course_create',
        'message' => '李老师创建了新课程：高等数学',
        'time' => '2025-08-22 14:20',
        'icon' => 'book'
    ],
    [
        'type' => 'leave_approve',
        'message' => '王老师审批了学生请假申请',
        'time' => '2025-08-22 13:15',
        'icon' => 'check-circle'
    ]
];

$pending_items = [
    [
        'type' => 'leave_requests',
        'count' => 8,
        'title' => '待审批请假申请',
        'url' => 'leaves'
    ],
    [
        'type' => 'user_approval',
        'count' => 3,
        'title' => '待审核用户',
        'url' => 'users'
    ],
    [
        'type' => 'course_review',
        'count' => 2,
        'title' => '待审核课程',
        'url' => 'courses'
    ]
];

// 安全输出函数
function safe_html($str) {
    return htmlspecialchars($str, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员主页 - 特靠谱教培系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
        }
        .main-content {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .activity-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }
        .pending-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        .pending-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            color: inherit;
            text-decoration: none;
        }
        .pending-count {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        .quick-action {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            text-decoration: none;
            color: inherit;
        }
        .quick-action:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            color: inherit;
            text-decoration: none;
        }
        .quick-action i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .revenue-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .revenue-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .growth-rate {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
        }

        /* 手机模式下的导航栏优化 */
        @media (max-width: 991.98px) {
            .navbar-nav .nav-link {
                padding: 0.5rem 0.75rem;
                text-align: center;
            }

            .navbar-nav .nav-link i {
                font-size: 1.2rem;
                margin-right: 0.5rem;
            }

            .navbar-toggler {
                border: none;
                padding: 0.25rem 0.5rem;
            }

            .navbar-collapse {
                background: rgba(255, 255, 255, 0.98);
                border-radius: 10px;
                margin-top: 0.5rem;
                padding: 0.5rem;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-graduation-cap"></i> 特靠谱教培系统
            </a>

            <!-- 手机模式下的切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto">
                    <a class="nav-link active" href="admin_test.php">
                        <i class="fas fa-home"></i> <span class="nav-text">主页</span>
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-users"></i> <span class="nav-text">用户管理</span>
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-book"></i> <span class="nav-text">课程管理</span>
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-chart-bar"></i> <span class="nav-text">数据统计</span>
                    </a>
                </div>

                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> <span class="nav-text"><?php echo safe_html($current_user['name']); ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../logout.php">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container main-content" style="margin-top: 80px;">
        <!-- 欢迎信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <h4 class="mb-3">欢迎回来，<?php echo safe_html($current_user['name']); ?>！</h4>
                    <p class="mb-0">今天是<span class="text-primary"><?php 
                        $weekdays = ['日', '一', '二', '三', '四', '五', '六'];
                        $today = date('Y年m月d日', time()) . ' 星期' . $weekdays[date('w', time())];
                        echo $today;
                    ?></span>，系统运行正常。</p>
                </div>
            </div>
        </div>

        <!-- 统计数据 -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6">
                <div class="stats-card text-center">
                    <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                    <div class="stat-label">总用户数</div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stats-card text-center">
                    <div class="stat-number"><?php echo $stats['total_courses']; ?></div>
                    <div class="stat-label">总课程数</div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stats-card text-center">
                    <div class="stat-number"><?php echo $stats['active_courses']; ?></div>
                    <div class="stat-label">活跃课程</div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stats-card text-center">
                    <div class="stat-number"><?php echo $stats['pending_leaves']; ?></div>
                    <div class="stat-label">待审批请假</div>
                </div>
            </div>
        </div>

        <!-- 收入统计 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="revenue-card">
                    <h5 class="mb-3"><i class="fas fa-chart-line"></i> 本月收入</h5>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="revenue-number">¥<?php echo number_format($stats['this_month_revenue']); ?></div>
                            <div class="mt-2">
                                <span class="growth-rate">
                                    <i class="fas fa-arrow-up"></i> +7.8%
                                </span>
                                <small class="ms-2">较上月增长</small>
                            </div>
                        </div>
                        <div>
                            <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stats-card">
                    <h5 class="mb-3"><i class="fas fa-users"></i> 用户分布</h5>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-number text-primary"><?php echo $stats['total_teachers']; ?></div>
                            <div class="stat-label">教师</div>
                        </div>
                        <div class="col-4">
                            <div class="stat-number text-success"><?php echo $stats['total_students']; ?></div>
                            <div class="stat-label">学生</div>
                        </div>
                        <div class="col-4">
                            <div class="stat-number text-warning">3</div>
                            <div class="stat-label">管理员</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 待处理事项 -->
            <div class="col-lg-4">
                <div class="stats-card">
                    <h5 class="mb-3"><i class="fas fa-tasks"></i> 待处理事项</h5>
                    <?php if (empty($pending_items)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>暂无待处理事项</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($pending_items as $item): ?>
                            <a href="#" class="pending-item d-block">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?php echo safe_html($item['title']); ?></h6>
                                        <small class="text-muted">需要您的处理</small>
                                    </div>
                                    <div class="pending-count">
                                        <?php echo $item['count']; ?>
                                    </div>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="col-lg-8">
                <div class="stats-card">
                    <h5 class="mb-3"><i class="fas fa-history"></i> 最近活动</h5>
                    <?php if (empty($recent_activities)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-clock fa-3x mb-3"></i>
                            <p>暂无最近活动</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex align-items-center">
                                    <div class="activity-icon">
                                        <i class="fas fa-<?php echo $activity['icon']; ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold"><?php echo safe_html($activity['message']); ?></div>
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> <?php echo safe_html($activity['time']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 快速功能 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="stats-card">
                    <h5 class="mb-3"><i class="fas fa-bolt"></i> 快速功能</h5>
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="#" class="quick-action d-block">
                                <i class="fas fa-users"></i>
                                <h6>用户管理</h6>
                                <p class="mb-0 text-muted">管理系统用户</p>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="#" class="quick-action d-block">
                                <i class="fas fa-book"></i>
                                <h6>课程管理</h6>
                                <p class="mb-0 text-muted">管理课程信息</p>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="#" class="quick-action d-block">
                                <i class="fas fa-chart-bar"></i>
                                <h6>数据统计</h6>
                                <p class="mb-0 text-muted">查看统计报表</p>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="#" class="quick-action d-block">
                                <i class="fas fa-cog"></i>
                                <h6>系统设置</h6>
                                <p class="mb-0 text-muted">系统配置管理</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
